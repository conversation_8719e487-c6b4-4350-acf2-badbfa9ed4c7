<script>
    // Resource Details Page Scripts - Following PM.Tool UI Design System v2.1
    
    (function() {
        'use strict';
        
        // Configuration and constants
        const CONFIG = {
            resourceId: @Model.Id,
            apiEndpoint: '@Url.Action("Details", "Resource", new { id = Model.Id })',
            allocationsEndpoint: '@Url.Action("Allocations", "Resource", new { id = Model.Id })',
            refreshInterval: 30000, // 30 seconds
            animationDuration: 300,
            storageKey: 'resource-details-preferences'
        };
        
        // Enhanced Resource Details Manager
        class ResourceDetailsManager {
            constructor() {
                this.activeTab = 'overview';
                this.refreshTimer = null;
                this.preferences = this.loadPreferences();
                this.isLoading = false;
                
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.setupTabNavigation();
                this.setupThemeHandling();
                this.setupAccessibility();
                this.setupAutoRefresh();
                this.setupKeyboardShortcuts();
                this.restorePreferences();
                this.initializeCharts();
            }
            
            bindEvents() {
                // Action button enhancements
                const actionButtons = document.querySelectorAll('.action-button');
                actionButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        this.handleActionClick(e, button);
                    });
                    
                    // Add ripple effect
                    button.addEventListener('mousedown', (e) => {
                        this.createRipple(e, button);
                    });
                });
                
                // Metrics badge interactions
                const metricsButtons = document.querySelectorAll('.metrics-badge');
                metricsButtons.forEach(badge => {
                    badge.addEventListener('click', (e) => {
                        this.handleMetricClick(e, badge);
                    });
                });
                
                // Status indicator interactions
                const statusIndicator = document.querySelector('.status-indicator');
                if (statusIndicator) {
                    statusIndicator.addEventListener('click', () => {
                        this.toggleResourceStatus();
                    });
                }
                
                // Refresh button
                const refreshButton = document.getElementById('refreshData');
                if (refreshButton) {
                    refreshButton.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.refreshData();
                    });
                }
            }
            
            setupTabNavigation() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');
                
                tabButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        const targetTab = button.dataset.tab;
                        this.switchTab(targetTab, tabButtons, tabContents);
                    });
                });
                
                // Keyboard navigation for tabs
                tabButtons.forEach((button, index) => {
                    button.addEventListener('keydown', (e) => {
                        let targetIndex = index;
                        
                        switch (e.key) {
                            case 'ArrowLeft':
                                e.preventDefault();
                                targetIndex = index > 0 ? index - 1 : tabButtons.length - 1;
                                break;
                            case 'ArrowRight':
                                e.preventDefault();
                                targetIndex = index < tabButtons.length - 1 ? index + 1 : 0;
                                break;
                            case 'Home':
                                e.preventDefault();
                                targetIndex = 0;
                                break;
                            case 'End':
                                e.preventDefault();
                                targetIndex = tabButtons.length - 1;
                                break;
                        }
                        
                        if (targetIndex !== index) {
                            tabButtons[targetIndex].focus();
                            tabButtons[targetIndex].click();
                        }
                    });
                });
            }
            
            setupThemeHandling() {
                // Listen for theme changes and update charts/visualizations
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            this.handleThemeChange();
                        }
                    });
                });
                
                observer.observe(document.documentElement, {
                    attributes: true,
                    attributeFilter: ['class']
                });
            }
            
            setupAccessibility() {
                // Add ARIA labels and descriptions
                const resourceHeader = document.querySelector('.resource-header');
                if (resourceHeader) {
                    resourceHeader.setAttribute('role', 'banner');
                    resourceHeader.setAttribute('aria-label', 'Resource information header');
                }
                
                const metricsBar = document.querySelector('.metrics-bar');
                if (metricsBar) {
                    metricsBar.setAttribute('role', 'region');
                    metricsBar.setAttribute('aria-label', 'Resource metrics and statistics');
                }
                
                // Add live region for dynamic updates
                const liveRegion = document.createElement('div');
                liveRegion.setAttribute('aria-live', 'polite');
                liveRegion.setAttribute('aria-atomic', 'true');
                liveRegion.className = 'sr-only';
                document.body.appendChild(liveRegion);
                this.liveRegion = liveRegion;
            }
            
            setupAutoRefresh() {
                // Auto-refresh data every 30 seconds
                this.refreshTimer = setInterval(() => {
                    if (!document.hidden && !this.isLoading) {
                        this.refreshData(true); // Silent refresh
                    }
                }, CONFIG.refreshInterval);
                
                // Pause refresh when page is hidden
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        clearInterval(this.refreshTimer);
                    } else {
                        this.setupAutoRefresh();
                    }
                });
            }
            
            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch (e.key) {
                            case 'r':
                                e.preventDefault();
                                this.refreshData();
                                break;
                            case 'e':
                                e.preventDefault();
                                this.navigateToEdit();
                                break;
                            case 'a':
                                e.preventDefault();
                                this.navigateToAllocations();
                                break;
                        }
                    }
                    
                    // Tab navigation shortcuts
                    if (e.altKey) {
                        const tabNumber = parseInt(e.key);
                        if (tabNumber >= 1 && tabNumber <= 9) {
                            e.preventDefault();
                            const tabButtons = document.querySelectorAll('.tab-button');
                            if (tabButtons[tabNumber - 1]) {
                                tabButtons[tabNumber - 1].click();
                            }
                        }
                    }
                });
            }
            
            switchTab(targetTab, tabButtons, tabContents) {
                // Update active states
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                    content.setAttribute('aria-hidden', 'true');
                });
                
                // Activate target tab
                const activeButton = document.querySelector(`[data-tab="${targetTab}"]`);
                const activeContent = document.getElementById(`${targetTab}-content`);
                
                if (activeButton && activeContent) {
                    activeButton.classList.add('active');
                    activeButton.setAttribute('aria-selected', 'true');
                    
                    // Animate content change
                    setTimeout(() => {
                        activeContent.classList.remove('hidden');
                        activeContent.setAttribute('aria-hidden', 'false');
                        activeContent.style.opacity = '0';
                        activeContent.style.transform = 'translateY(10px)';
                        
                        requestAnimationFrame(() => {
                            activeContent.style.transition = 'all 0.3s ease';
                            activeContent.style.opacity = '1';
                            activeContent.style.transform = 'translateY(0)';
                        });
                    }, 50);
                    
                    this.activeTab = targetTab;
                    this.savePreferences();
                    
                    // Announce tab change
                    if (this.liveRegion) {
                        this.liveRegion.textContent = `Switched to ${targetTab} tab`;
                    }
                }
            }
            
            handleActionClick(e, button) {
                // Add loading state
                const originalContent = button.innerHTML;
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
                
                // Simulate action completion (replace with actual logic)
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = originalContent;
                    
                    // Show success feedback
                    this.showNotification('Action completed successfully', 'success');
                }, 1000);
            }
            
            createRipple(e, button) {
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
            
            handleMetricClick(e, badge) {
                // Add click animation
                badge.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    badge.style.transform = '';
                }, 150);
                
                // Show detailed metric information
                const metricType = badge.dataset.metric;
                this.showMetricDetails(metricType);
            }
            
            refreshData(silent = false) {
                if (this.isLoading) return;
                
                this.isLoading = true;
                
                if (!silent) {
                    this.showLoadingState();
                }
                
                // Simulate API call (replace with actual implementation)
                setTimeout(() => {
                    this.isLoading = false;
                    
                    if (!silent) {
                        this.hideLoadingState();
                        this.showNotification('Data refreshed successfully', 'info');
                    }
                    
                    // Update last refresh time
                    this.updateLastRefreshTime();
                }, 1000);
            }
            
            showLoadingState() {
                const loadingElements = document.querySelectorAll('.metrics-badge, .content-section');
                loadingElements.forEach(el => {
                    el.classList.add('details-loading');
                });
            }
            
            hideLoadingState() {
                const loadingElements = document.querySelectorAll('.details-loading');
                loadingElements.forEach(el => {
                    el.classList.remove('details-loading');
                });
            }
            
            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;
                
                const colors = {
                    success: 'bg-emerald-500 text-white',
                    error: 'bg-red-500 text-white',
                    info: 'bg-blue-500 text-white',
                    warning: 'bg-amber-500 text-white'
                };
                
                notification.className += ` ${colors[type] || colors.info}`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Animate in
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0)';
                });
                
                // Auto remove
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }
            
            handleThemeChange() {
                const isDark = document.documentElement.classList.contains('dark');
                
                // Update any theme-dependent visualizations
                this.updateChartsTheme(isDark);
                
                console.log(`Resource details theme changed to: ${isDark ? 'dark' : 'light'}`);
            }
            
            updateChartsTheme(isDark) {
                // Update chart colors and styling based on theme
                // This would integrate with your charting library
            }
            
            initializeCharts() {
                // Initialize any charts or data visualizations
                // This would integrate with your preferred charting library
            }
            
            navigateToEdit() {
                window.location.href = '@Url.Action("Edit", "Resource", new { id = Model.Id })';
            }
            
            navigateToAllocations() {
                window.location.href = '@Url.Action("Allocations", "Resource", new { id = Model.Id })';
            }
            
            updateLastRefreshTime() {
                const timeElement = document.getElementById('lastRefreshTime');
                if (timeElement) {
                    timeElement.textContent = new Date().toLocaleTimeString();
                }
            }
            
            savePreferences() {
                const preferences = {
                    activeTab: this.activeTab
                };
                localStorage.setItem(CONFIG.storageKey, JSON.stringify(preferences));
            }
            
            loadPreferences() {
                try {
                    const stored = localStorage.getItem(CONFIG.storageKey);
                    return stored ? JSON.parse(stored) : {};
                } catch (e) {
                    console.warn('Failed to load preferences:', e);
                    return {};
                }
            }
            
            restorePreferences() {
                if (this.preferences.activeTab) {
                    const tabButton = document.querySelector(`[data-tab="${this.preferences.activeTab}"]`);
                    if (tabButton) {
                        tabButton.click();
                    }
                }
            }
            
            destroy() {
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                }
                this.savePreferences();
            }
        }
        
        // Add ripple animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @@keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.resourceDetailsManager = new ResourceDetailsManager();
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.resourceDetailsManager) {
                window.resourceDetailsManager.destroy();
            }
        });
        
    })();
</script>
