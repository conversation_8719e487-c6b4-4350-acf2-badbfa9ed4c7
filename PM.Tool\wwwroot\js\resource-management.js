/**
 * Resource Management Enhanced JavaScript
 * Following PM.Tool UI Design System & Theme Guidelines v2.0
 */

class ResourceManager {
    constructor() {
        this.currentView = 'grid';
        this.currentSort = 'name';
        this.filters = {
            search: '',
            type: '',
            status: '',
            department: '',
            location: ''
        };
        this.resources = [];
        this.filteredResources = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadResources();
        this.populateFilterDropdowns();
        this.setupQuickFilters();
        this.setupViewControls();
        this.setupSorting();
    }

    bindEvents() {
        // Search input with debounce
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filters.search = e.target.value.toLowerCase();
                    this.applyFilters();
                }, 300);
            });
        }

        // Filter dropdowns
        ['typeFilter', 'statusFilter', 'departmentFilter', 'locationFilter'].forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterKey = filterId.replace('Filter', '');
                    this.filters[filterKey] = e.target.value;
                    this.applyFilters();
                });
            }
        });

        // Clear filters button
        const clearFilters = document.getElementById('clearFilters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }

        // Sort dropdown
        const sortBy = document.getElementById('sortBy');
        if (sortBy) {
            sortBy.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.applySorting();
            });
        }
    }

    loadResources() {
        // Extract resource data from DOM
        const resourceCards = document.querySelectorAll('.resource-card');
        this.resources = Array.from(resourceCards).map(card => ({
            element: card,
            name: card.querySelector('h3')?.textContent?.toLowerCase() || '',
            type: card.dataset.type || '',
            status: card.dataset.status === 'true',
            department: card.dataset.department || '',
            location: card.dataset.location || '',
            rate: parseFloat(card.dataset.rate) || 0
        }));
        this.filteredResources = [...this.resources];
    }

    populateFilterDropdowns() {
        // Populate department filter
        const departments = [...new Set(this.resources.map(r => r.department).filter(d => d))];
        const departmentFilter = document.getElementById('departmentFilter');
        if (departmentFilter && departments.length > 0) {
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });
        }

        // Populate location filter
        const locations = [...new Set(this.resources.map(r => r.location).filter(l => l))];
        const locationFilter = document.getElementById('locationFilter');
        if (locationFilter && locations.length > 0) {
            locations.forEach(loc => {
                const option = document.createElement('option');
                option.value = loc;
                option.textContent = loc;
                locationFilter.appendChild(option);
            });
        }
    }

    setupQuickFilters() {
        const quickFilterButtons = document.querySelectorAll('.quick-filter-btn');
        quickFilterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Toggle active state
                const isActive = button.classList.contains('active');
                
                // Remove active state from all quick filter buttons
                quickFilterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-primary-50', 'dark:bg-primary-900/20', 
                                        'text-primary-700', 'dark:text-primary-300', 
                                        'border-primary-300', 'dark:border-primary-600');
                });

                if (!isActive) {
                    // Add active state to clicked button
                    button.classList.add('active', 'bg-primary-50', 'dark:bg-primary-900/20', 
                                        'text-primary-700', 'dark:text-primary-300', 
                                        'border-primary-300', 'dark:border-primary-600');
                    
                    const filterType = button.dataset.filter;
                    this.applyQuickFilter(filterType);
                } else {
                    this.clearAllFilters();
                }
            });
        });
    }

    setupViewControls() {
        const viewButtons = document.querySelectorAll('#gridView, #listView, #tableView');
        viewButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Update active state
                viewButtons.forEach(btn => {
                    btn.classList.remove('bg-white', 'dark:bg-neutral-700', 'text-primary-600', 
                                        'dark:text-primary-400', 'shadow-sm');
                    btn.classList.add('text-neutral-600', 'dark:text-neutral-400');
                });
                
                button.classList.add('bg-white', 'dark:bg-neutral-700', 'text-primary-600', 
                                   'dark:text-primary-400', 'shadow-sm');
                button.classList.remove('text-neutral-600', 'dark:text-neutral-400');
                
                // Update view
                this.currentView = button.id.replace('View', '');
                this.updateView();
            });
        });
    }

    setupSorting() {
        // Initial sort
        this.applySorting();
    }

    applyQuickFilter(filterType) {
        this.clearAllFilters();
        
        switch (filterType) {
            case 'active':
                this.filters.status = 'true';
                document.getElementById('statusFilter').value = 'true';
                break;
            case 'human':
                this.filters.type = 'human';
                document.getElementById('typeFilter').value = 'Human';
                break;
            case 'equipment':
                this.filters.type = 'equipment';
                document.getElementById('typeFilter').value = 'Equipment';
                break;
            case 'available':
                this.filters.status = 'true';
                document.getElementById('statusFilter').value = 'true';
                break;
        }
        
        this.applyFilters();
    }

    applyFilters() {
        this.filteredResources = this.resources.filter(resource => {
            // Search filter
            if (this.filters.search && !resource.name.includes(this.filters.search)) {
                return false;
            }
            
            // Type filter
            if (this.filters.type && resource.type !== this.filters.type.toLowerCase()) {
                return false;
            }
            
            // Status filter
            if (this.filters.status !== '' && resource.status !== (this.filters.status === 'true')) {
                return false;
            }
            
            // Department filter
            if (this.filters.department && resource.department !== this.filters.department) {
                return false;
            }
            
            // Location filter
            if (this.filters.location && resource.location !== this.filters.location) {
                return false;
            }
            
            return true;
        });
        
        this.applySorting();
        this.updateResourceDisplay();
        this.updateResourceCount();
    }

    applySorting() {
        this.filteredResources.sort((a, b) => {
            switch (this.currentSort) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'type':
                    return a.type.localeCompare(b.type);
                case 'department':
                    return a.department.localeCompare(b.department);
                case 'rate':
                    return b.rate - a.rate; // Descending order for rate
                case 'capacity':
                    // Would need capacity data in dataset
                    return a.name.localeCompare(b.name);
                default:
                    return 0;
            }
        });
    }

    updateResourceDisplay() {
        // Hide all resources first
        this.resources.forEach(resource => {
            resource.element.style.display = 'none';
        });
        
        // Show filtered resources
        this.filteredResources.forEach(resource => {
            resource.element.style.display = 'block';
        });
        
        // Update view layout
        this.updateView();
    }

    updateView() {
        const container = document.getElementById('resourceContainer');
        if (!container) return;
        
        switch (this.currentView) {
            case 'grid':
                container.className = 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8';
                break;
            case 'list':
                container.className = 'space-y-4';
                break;
            case 'table':
                // Would need table implementation
                container.className = 'space-y-2';
                break;
        }
    }

    updateResourceCount() {
        const countElement = document.getElementById('resourceCount');
        if (countElement) {
            countElement.textContent = this.filteredResources.length;
        }
    }

    clearAllFilters() {
        // Reset filters
        this.filters = {
            search: '',
            type: '',
            status: '',
            department: '',
            location: ''
        };
        
        // Reset form elements
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('departmentFilter').value = '';
        document.getElementById('locationFilter').value = '';
        
        // Remove active states from quick filters
        document.querySelectorAll('.quick-filter-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-primary-50', 'dark:bg-primary-900/20', 
                               'text-primary-700', 'dark:text-primary-300', 
                               'border-primary-300', 'dark:border-primary-600');
        });
        
        this.applyFilters();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('resourceContainer')) {
        new ResourceManager();
    }
});

// Export for use in other modules
window.ResourceManager = ResourceManager;
