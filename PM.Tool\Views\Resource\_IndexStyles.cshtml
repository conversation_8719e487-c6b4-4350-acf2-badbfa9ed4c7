<style>
    /* Resource Index Page Styles - Following PM.Tool UI Design System v2.1 */

    /* 1. Page-specific component styles following design guidelines */
    .resource-index-container {
        @@apply space-y-6;
    }

    /* Header styling with multi-line support */
    .page-header {
        @@apply mb-6;
    }

    /* Compact filter bar */
    .filter-bar {
        @@apply mb-4;
    }

    /* Multi-line flex containers */
    .flex-wrap-multiline {
        /* Ensure proper spacing when items wrap to multiple lines */
        gap: 1rem 1.5rem; /* row-gap column-gap */
    }

    /* Analytics row with dedicated space */
    .analytics-row {
        /* Dedicated row for analytics badges with proper spacing */
        background-color: rgb(249 250 251);
        border-top: 1px solid rgb(229 231 235);
    }

    .dark .analytics-row {
        background-color: rgb(64 64 64 / 0.5);
        border-top-color: rgb(115 115 115);
    }

    /* Stats badges with optimal wrapping in dedicated space */
    .analytics-badges {
        /* Allow badges to wrap naturally with full width available */
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 0.75rem 1rem;
    }

    /* Action buttons in clean single row */
    .action-buttons-row {
        /* Keep buttons in single row with overflow dropdown */
        display: flex;
        align-items: center;
        gap: 1.25rem;
        flex-shrink: 0;
    }

    /* More actions dropdown */
    .more-actions-dropdown {
        /* Smooth dropdown animation */
        transform: translateY(-10px);
        opacity: 0;
        transition: all 0.2s ease;
    }

    .more-actions-dropdown:not(.hidden) {
        transform: translateY(0);
        opacity: 1;
    }

    /* Filter controls with organized layout */
    .filter-controls-grid {
        /* Grid that adapts to content and screen size */
        display: grid;
        gap: 1.5rem;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    /* Quick filter buttons with proper spacing */
    .quick-filters-container {
        /* Allow filters to wrap while maintaining visual grouping */
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        align-items: center;
    }

    /* View controls with smooth transitions */
    .view-controls-container {
        /* Smooth transitions for view changes */
        transition: all 0.3s ease;
    }

    /* Resource container view transitions */
    #resourceContainer {
        /* Smooth layout transitions */
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* View-specific styles */
    .view-grid .resource-card {
        /* Grid view optimizations */
        transition: all 0.3s ease;
        transform: translateZ(0); /* Enable hardware acceleration */
    }

    .view-list .resource-card {
        /* List view optimizations */
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .view-table .resource-card {
        /* Table view optimizations */
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        border-radius: 0.5rem;
    }

    /* View button states */
    .view-button {
        /* Smooth state transitions */
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .view-button.active {
        /* Active state styling */
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .view-button:hover:not(.active) {
        /* Hover state for inactive buttons */
        transform: translateY(-1px);
        background-color: rgba(255, 255, 255, 0.8);
    }

    .dark .view-button:hover:not(.active) {
        background-color: rgba(64, 64, 64, 0.8);
    }

    /* Smooth opacity transitions for view changes */
    .view-transition {
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .view-transition.complete {
        opacity: 1;
    }

    /* Compact filter styles */
    .filter-bar {
        /* Reduced padding for compact design */
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .quick-filter-btn {
        /* Compact button styling */
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        white-space: nowrap;
    }

    .quick-filter-btn.active {
        /* Active state for quick filters */
        background-color: rgb(59 130 246);
        border-color: rgb(59 130 246);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .dark .quick-filter-btn.active {
        background-color: rgb(96 165 250);
        border-color: rgb(96 165 250);
    }

    /* Advanced filters animation */
    #advancedFilters {
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #advancedFilters.hidden {
        max-height: 0 !important;
        opacity: 0 !important;
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    /* Compact form controls */
    .compact-select {
        font-size: 0.75rem;
        padding: 0.375rem 0.625rem;
    }

    .compact-input {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    /* Active filter tags */
    .active-filter-tag {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        background-color: rgb(239 246 255);
        color: rgb(59 130 246);
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .dark .active-filter-tag {
        background-color: rgb(30 58 138);
        color: rgb(147 197 253);
    }

    .active-filter-tag:hover {
        background-color: rgb(219 234 254);
        transform: scale(1.05);
    }

    .dark .active-filter-tag:hover {
        background-color: rgb(30 64 175);
    }

    .active-filter-tag button {
        margin-left: 0.375rem;
        color: rgb(107 114 128);
        hover:color: rgb(239 68 68);
        transition: color 0.2s ease;
    }

    .dark .active-filter-tag button {
        color: rgb(156 163 175);
    }

    .dark .active-filter-tag button:hover {
        color: rgb(248 113 113);
    }

    .filter-tag:hover {
        transform: scale(1.05);
    }

    /* 2. Enhanced filter animations */
    .quick-filter-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .dark .quick-filter-btn:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        border-color: #2563eb;
        box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        box-shadow: 0 4px 14px 0 rgba(96, 165, 250, 0.3);
    }

    /* 3. Resource card enhancements */
    .resource-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .resource-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .dark .resource-card:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    }

    .resource-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .resource-card:hover::before {
        opacity: 1;
    }

    /* 4. View controls styling */
    .view-control-btn {
        transition: all 0.2s ease;
        position: relative;
    }

    .view-control-btn.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: var(--primary-500);
        border-radius: 1px;
    }

    /* 5. Filter bar enhancements */
    .filter-bar {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    .dark .filter-bar {
        background: rgba(31, 41, 55, 0.95);
    }

    /* 6. Search input enhancements */
    .search-input-container {
        position: relative;
    }

    .search-input-container::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .search-input-container:focus-within::after {
        transform: scaleX(1);
    }

    /* 7. Stats badge animations */
    .stats-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s ease;
    }

    .stats-badge:hover::before {
        left: 100%;
    }

    /* 8. Loading states */
    .resource-loading {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: .5;
        }
    }

    /* 9. Empty state enhancements */
    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* 10. Responsive adjustments */
    @@media (max-width: 768px) {
        .resource-card {
            margin-bottom: 1rem;
        }

        .filter-bar {
            padding: 1rem;
        }

        .quick-filter-btn {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .stats-badge {
            font-size: 0.625rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* 11. Print styles */
    @@media print {
        .filter-bar,
        .view-controls,
        .resource-card .card-footer {
            display: none !important;
        }

        .resource-card {
            break-inside: avoid;
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
        }
    }

    /* 12. High contrast mode support */
    @@media (prefers-contrast: high) {
        .resource-card {
            border-width: 2px;
        }

        .quick-filter-btn {
            border-width: 2px;
        }

        .stats-badge {
            border: 1px solid currentColor;
        }
    }

    /* 13. Responsive adjustments */
    @@media (max-width: 768px) {
        .resource-card {
            margin-bottom: 1rem;
        }

        .page-header {
            margin-bottom: 1rem;
        }

        .page-header .px-6 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .filter-bar {
            margin-bottom: 1rem;
        }

        .filter-bar .p-4 {
            padding: 0.75rem;
        }

        .quick-filter-btn {
            font-size: 0.625rem;
            padding: 0.375rem 0.5rem;
        }

        .stats-badge {
            font-size: 0.625rem;
            padding: 0.25rem 0.5rem;
        }

        /* Stack filter elements on mobile */
        .filter-bar .flex.flex-wrap {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        /* Make search full width on mobile */
        .filter-bar .max-w-sm {
            max-width: 100%;
        }

        /* Adjust view controls for mobile */
        .view-control-btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.625rem;
        }
    }

    /* 14. Legacy compatibility styles */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 15. Reduced motion support */
    @@media (prefers-reduced-motion: reduce) {
        .resource-card,
        .quick-filter-btn,
        .stats-badge {
            transition: none;
        }

        .empty-state-icon {
            animation: none;
        }

        .resource-loading {
            animation: none;
        }
    }
</style>
