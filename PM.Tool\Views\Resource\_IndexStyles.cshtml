<style>
    /* Resource Index Page Styles - Following PM.Tool UI Design System v2.1 */

    /* 1. Page-specific component styles */
    .resource-index-container {
        @@apply space-y-8;
    }

    /* 2. Enhanced filter animations */
    .quick-filter-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .dark .quick-filter-btn:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        border-color: #2563eb;
        box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        box-shadow: 0 4px 14px 0 rgba(96, 165, 250, 0.3);
    }

    /* 3. Resource card enhancements */
    .resource-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .resource-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .dark .resource-card:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    }

    .resource-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .resource-card:hover::before {
        opacity: 1;
    }

    /* 4. View controls styling */
    .view-control-btn {
        transition: all 0.2s ease;
        position: relative;
    }

    .view-control-btn.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: var(--primary-500);
        border-radius: 1px;
    }

    /* 5. Filter bar enhancements */
    .filter-bar {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    .dark .filter-bar {
        background: rgba(31, 41, 55, 0.95);
    }

    /* 6. Search input enhancements */
    .search-input-container {
        position: relative;
    }

    .search-input-container::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .search-input-container:focus-within::after {
        transform: scaleX(1);
    }

    /* 7. Stats badge animations */
    .stats-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s ease;
    }

    .stats-badge:hover::before {
        left: 100%;
    }

    /* 8. Loading states */
    .resource-loading {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: .5;
        }
    }

    /* 9. Empty state enhancements */
    .empty-state-icon {
        animation: float 3s ease-in-out infinite;
    }

    @@keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* 10. Responsive adjustments */
    @@media (max-width: 768px) {
        .resource-card {
            margin-bottom: 1rem;
        }

        .filter-bar {
            padding: 1rem;
        }

        .quick-filter-btn {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .stats-badge {
            font-size: 0.625rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* 11. Print styles */
    @@media print {
        .filter-bar,
        .view-controls,
        .resource-card .card-footer {
            display: none !important;
        }

        .resource-card {
            break-inside: avoid;
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
        }
    }

    /* 12. High contrast mode support */
    @@media (prefers-contrast: high) {
        .resource-card {
            border-width: 2px;
        }

        .quick-filter-btn {
            border-width: 2px;
        }

        .stats-badge {
            border: 1px solid currentColor;
        }
    }

    /* 13. Legacy compatibility styles */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 14. Reduced motion support */
    @@media (prefers-reduced-motion: reduce) {
        .resource-card,
        .quick-filter-btn,
        .stats-badge {
            transition: none;
        }

        .empty-state-icon {
            animation: none;
        }

        .resource-loading {
            animation: none;
        }
    }
</style>
