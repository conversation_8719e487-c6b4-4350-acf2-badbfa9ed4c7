@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Resource>
@{
    ViewData["Title"] = "Resource Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Resource Management", Href = "", Icon = "fas fa-users-cog" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header Following Design Guidelines -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 shadow-sm dark:shadow-lg">
    <!-- Title and Actions Row -->
    <div class="flex items-center justify-between px-10 py-8 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-10">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight flex items-center">
                <i class="fas fa-users-cog mr-3 text-primary-600 dark:text-primary-400"></i>
                Resource Management
            </h1>
        </div>

        <!-- Action Buttons - Clean single row -->
        <div class="flex items-center space-x-5 flex-shrink-0">
            @{
                ViewData["Text"] = "Add Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Schedule View";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-calendar";
                ViewData["Href"] = Url.Action("Schedule");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Capacity Planning";
                ViewData["Variant"] = "tertiary";
                ViewData["Icon"] = "fas fa-chart-bar";
                ViewData["Href"] = Url.Action("Capacity");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- More actions dropdown for additional buttons -->
            <div class="relative">
                @{
                    ViewData["Text"] = "";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-ellipsis-h";
                    ViewData["Href"] = null; // Make it a button, not a link
                    ViewData["Size"] = "medium";
                    ViewData["Id"] = "moreActionsBtn";
                    ViewData["Type"] = "button";
                    ViewData["Title"] = "More actions";
                    ViewData["AriaLabel"] = "More actions";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <div id="moreActionsMenu" class="hidden absolute right-0 top-12 w-48 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg z-10">
                    <div class="py-2">
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700">
                            <i class="fas fa-download mr-3 text-neutral-500"></i>Export Data
                        </a>
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700">
                            <i class="fas fa-upload mr-3 text-neutral-500"></i>Import Resources
                        </a>
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700">
                            <i class="fas fa-cog mr-3 text-neutral-500"></i>Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics and Description Row - Integrated -->
    <div class="px-10 py-6 bg-neutral-50 dark:bg-neutral-700/50">
        <!-- Description -->
        <div class="mb-4">
            <p class="text-sm text-neutral-600 dark:text-neutral-300">
                Manage team resources, allocations, and capacity planning with comprehensive tracking and analytics
            </p>
        </div>

        <!-- Analytics Badges -->
        <div class="flex flex-wrap items-center gap-x-4 gap-y-3">
            <span class="inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                <i class="fas fa-users mr-2 text-blue-600 dark:text-blue-400"></i>@Model.Count() Total Resources
            </span>
            <span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-xs font-medium">
                <i class="fas fa-user-check mr-2 text-emerald-600 dark:text-emerald-400"></i>@Model.Count(r => r.IsActive) Active
            </span>
            <span class="inline-flex items-center px-3 py-1.5 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 rounded-full text-xs font-medium">
                <i class="fas fa-user-tie mr-2 text-purple-600 dark:text-purple-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Human) People
            </span>
            <span class="inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 rounded-full text-xs font-medium">
                <i class="fas fa-tools mr-2 text-amber-600 dark:text-amber-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Equipment) Equipment
            </span>
            @if (Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Material) > 0)
            {
                <span class="inline-flex items-center px-3 py-1.5 bg-cyan-100 dark:bg-cyan-900/50 text-cyan-800 dark:text-cyan-200 rounded-full text-xs font-medium">
                    <i class="fas fa-boxes mr-2 text-cyan-600 dark:text-cyan-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Material) Materials
                </span>
            }
            @if (Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Facility) > 0)
            {
                <span class="inline-flex items-center px-3 py-1.5 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 rounded-full text-xs font-medium">
                    <i class="fas fa-building mr-2 text-indigo-600 dark:text-indigo-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Facility) Facilities
                </span>
            }
            @{
                var utilizationRate = Model.Count(r => r.IsActive) > 0 ? (Model.Count(r => r.IsActive) * 100 / Model.Count()) : 0;
            }
            <span class="inline-flex items-center px-3 py-1.5 bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200 rounded-full text-xs font-medium">
                <i class="fas fa-chart-line mr-2 text-orange-600 dark:text-orange-400"></i>@utilizationRate% Utilization
            </span>
        </div>
    </div>
</div>



<!-- Compact Filter Bar -->
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-6">
    <div class="px-6 py-4">
        <!-- Single Row: Search + Quick Filters + Advanced Toggle -->
        <div class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="relative flex-1 min-w-64 max-w-md">
                <input type="text" id="searchInput" class="w-full pl-9 pr-4 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" placeholder="Search resources...">
                <div class="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                    <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-xs"></i>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="flex flex-wrap items-center gap-2">
                <button type="button" class="quick-filter-btn px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="active">
                    <i class="fas fa-user-check mr-1.5 text-emerald-500"></i>Active
                </button>
                <button type="button" class="quick-filter-btn px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="human">
                    <i class="fas fa-user-tie mr-1.5 text-blue-500"></i>People
                </button>
                <button type="button" class="quick-filter-btn px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="equipment">
                    <i class="fas fa-tools mr-1.5 text-amber-500"></i>Equipment
                </button>
                <button type="button" class="quick-filter-btn px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="available">
                    <i class="fas fa-calendar-check mr-1.5 text-emerald-500"></i>Available
                </button>
            </div>

            <!-- Advanced Filters Toggle -->
            <button type="button" id="toggleAdvancedFilters" class="inline-flex items-center px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200">
                <i class="fas fa-sliders-h mr-1.5"></i>
                More
                <i class="fas fa-chevron-down ml-1.5 transition-transform duration-200" id="filtersToggleIcon"></i>
            </button>

            <!-- Clear Filters -->
            <button type="button" id="clearFilters" class="inline-flex items-center px-3 py-2 text-xs font-medium rounded-md border border-neutral-300 dark:border-neutral-600 text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200">
                <i class="fas fa-times mr-1.5"></i>Clear
            </button>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div id="advancedFilters" class="hidden mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-700">
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-x-4 gap-y-3">
                <!-- Row 1: Primary Filters -->
                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Type</label>
                    <select id="typeFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Types</option>
                        <option value="Human">Human</option>
                        <option value="Equipment">Equipment</option>
                        <option value="Material">Material</option>
                        <option value="Facility">Facility</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Status</label>
                    <select id="statusFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Statuses</option>
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Department</label>
                    <select id="departmentFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Departments</option>
                        <option value="Engineering">Engineering</option>
                        <option value="Design">Design</option>
                        <option value="Marketing">Marketing</option>
                        <option value="Sales">Sales</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Location</label>
                    <select id="locationFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Locations</option>
                        <option value="New York">New York</option>
                        <option value="San Francisco">San Francisco</option>
                        <option value="London">London</option>
                        <option value="Remote">Remote</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Skills</label>
                    <select id="skillFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Skills</option>
                        <option value="JavaScript">JavaScript</option>
                        <option value="Python">Python</option>
                        <option value="Design">Design</option>
                        <option value="Management">Management</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Availability</label>
                    <select id="availabilityFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Availability</option>
                        <option value="available">Available</option>
                        <option value="partial">Partially Available</option>
                        <option value="busy">Fully Allocated</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Experience</label>
                    <select id="experienceFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Levels</option>
                        <option value="junior">Junior (0-2 years)</option>
                        <option value="mid">Mid (3-5 years)</option>
                        <option value="senior">Senior (6+ years)</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Cost Center</label>
                    <select id="costCenterFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Centers</option>
                        <option value="R&D">R&D</option>
                        <option value="Operations">Operations</option>
                        <option value="Support">Support</option>
                    </select>
                </div>
            </div>

            <!-- Additional Filter Row for Date Ranges and Custom Filters -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-3 mt-3">
                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Hourly Rate Range</label>
                    <div class="flex items-center gap-2">
                        <input type="number" id="minRateFilter" placeholder="Min" class="w-full px-2 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <span class="text-xs text-neutral-500">-</span>
                        <input type="number" id="maxRateFilter" placeholder="Max" class="w-full px-2 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Last Active</label>
                    <select id="lastActiveFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Any Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                        <option value="quarter">This Quarter</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Certification</label>
                    <select id="certificationFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Certifications</option>
                        <option value="pmp">PMP Certified</option>
                        <option value="agile">Agile Certified</option>
                        <option value="aws">AWS Certified</option>
                        <option value="azure">Azure Certified</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">Project Count</label>
                    <select id="projectCountFilter" class="w-full px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Any Count</option>
                        <option value="0">No Projects</option>
                        <option value="1-3">1-3 Projects</option>
                        <option value="4-6">4-6 Projects</option>
                        <option value="7+">7+ Projects</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compact View Controls -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl px-6 py-4 mb-6">
    <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- Left: View Toggle and Results -->
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-3">
                <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">View:</span>
                <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1 view-controls-container">
                    <button type="button" id="gridView" class="view-button px-3 py-1.5 text-xs font-medium rounded-md bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1">
                        <i class="fas fa-th mr-1.5"></i>Grid
                    </button>
                    <button type="button" id="listView" class="view-button px-3 py-1.5 text-xs font-medium rounded-md text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1">
                        <i class="fas fa-list mr-1.5"></i>List
                    </button>
                    <button type="button" id="tableView" class="view-button px-3 py-1.5 text-xs font-medium rounded-md text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1">
                        <i class="fas fa-table mr-1.5"></i>Table
                    </button>
                </div>
            </div>

            <div class="text-sm text-neutral-600 dark:text-neutral-400">
                <span id="resourceCount">@Model.Count()</span> resources
            </div>
        </div>

        <!-- Right: Sort and Display Options -->
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-2">
                <label for="sortBy" class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Sort:</label>
                <select id="sortBy" class="px-2.5 py-1.5 text-xs border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500">
                    <option value="name">Name</option>
                    <option value="type">Type</option>
                    <option value="department">Department</option>
                    <option value="rate">Rate</option>
                    <option value="capacity">Capacity</option>
                </select>
            </div>

            <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Density:</span>
                <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-0.5">
                    <button type="button" id="compactView" class="px-2 py-1 text-xs font-medium rounded text-neutral-600 dark:text-neutral-400 hover:bg-white dark:hover:bg-neutral-700 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500" title="Compact">
                        <i class="fas fa-compress-alt"></i>
                    </button>
                    <button type="button" id="comfortableView" class="px-2 py-1 text-xs font-medium rounded bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500" title="Comfortable">
                        <i class="fas fa-expand-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Filters Display (Compact) -->
    <div id="activeFilters" class="hidden mt-3 pt-3 border-t border-neutral-200 dark:border-neutral-700">
        <div class="flex flex-wrap items-center gap-2">
            <span class="text-xs font-medium text-neutral-600 dark:text-neutral-400">Filters:</span>
            <div id="activeFiltersList" class="flex flex-wrap gap-1.5">
                <!-- Active filter tags will be inserted here by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Resource List -->
<div class="px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="resourceContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var resource in Model)
        {
            <div class="resource-card transition-all duration-200 hover:shadow-md dark:hover:shadow-lg hover:-translate-y-0.5"
                 data-type="@resource.Type.ToString().ToLower()"
                 data-status="@resource.IsActive.ToString().ToLower()"
                 data-department="@(resource.Department ?? "")"
                 data-location="@(resource.Location ?? "")">
                <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm dark:shadow-lg">
                    <div class="px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6">
                                <div class="w-12 h-12 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                                    <i class="@GetResourceTypeIcon(resource.Type) text-white text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@resource.Name</h3>
                                    <p class="text-sm text-neutral-600 dark:text-neutral-300 mt-1">@resource.Type</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                @if (resource.IsActive)
                                {
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200">
                                        <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200">
                                        <i class="fas fa-times-circle mr-2 text-neutral-600 dark:text-neutral-400"></i>Inactive
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="px-8 py-6 space-y-6">
                        @if (!string.IsNullOrEmpty(resource.Description))
                        {
                            <p class="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">@resource.Description</p>
                        }

                        <!-- Resource Details -->
                        <div class="space-y-4">
                            @if (!string.IsNullOrEmpty(resource.Department))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-building w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Department</span>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(resource.Location))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-map-marker-alt w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Location</span>
                                </div>
                            }
                            @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human)
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-clock w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Capacity hrs/day</span>
                                </div>
                                @if (resource.HourlyRate > 0)
                                {
                                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                        <i class="fas fa-dollar-sign w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                        <span>@resource.HourlyRate.ToString("C")/hr</span>
                                    </div>
                                }
                            }
                        </div>

                        <!-- Skills (for Human resources) -->
                        @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human && !string.IsNullOrEmpty(resource.Skills))
                        {
                            <div>
                                <h4 class="text-xs font-semibold text-neutral-500 dark:text-neutral-400 mb-3">Skills</h4>
                                <div class="flex flex-wrap gap-2">
                                    @{
                                        var skills = resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries);
                                        var displaySkills = skills.Take(3);
                                        var remainingCount = skills.Length - 3;
                                    }
                                    @foreach (var skill in displaySkills)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                                            @skill.Trim()
                                        </span>
                                    }
                                    @if (remainingCount > 0)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300">
                                            +@remainingCount more
                                        </span>
                                    }
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Card Footer with Actions -->
                    <div class="px-8 py-6 bg-neutral-50 dark:bg-neutral-700/50 border-t border-neutral-200 dark:border-neutral-700 rounded-b-xl">
                        <div class="flex items-center justify-between">
                            <small class="text-neutral-500 dark:text-neutral-400">
                                Updated @(resource.UpdatedAt?.ToString("MMM dd") ?? "N/A")
                            </small>
                            <div class="flex items-center space-x-2">
                                <a asp-action="Details" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 hover:border-blue-300 dark:hover:border-blue-600 rounded-lg transition-all duration-200"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 hover:text-amber-600 dark:hover:text-amber-400 hover:border-amber-300 dark:hover:border-amber-600 rounded-lg transition-all duration-200"
                                   title="Edit Resource">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Allocations" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 hover:border-purple-300 dark:hover:border-purple-600 rounded-lg transition-all duration-200"
                                   title="View Allocations">
                                    <i class="fas fa-calendar-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full text-center py-20">
            <div class="w-20 h-20 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-users-cog text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-3">No Resources Found</h3>
            <p class="text-neutral-600 dark:text-neutral-400 mb-8 max-w-md mx-auto">
                Start building your team by adding resources like team members, equipment, and facilities to your project.
            </p>
            @{
                ViewData["Text"] = "Add Your First Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
                ViewData["Size"] = "large";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
    </div>
</div>

@section Styles {
    <partial name="_IndexStyles" />
}

@section Scripts {
    <partial name="_IndexScripts" />
}



@functions {
    string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-question"
        };
    }

    string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-primary-500 to-primary-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-warning-500 to-warning-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-info-500 to-info-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-success-500 to-success-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
