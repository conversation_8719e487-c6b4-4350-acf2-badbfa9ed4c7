@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Resource>
@{
    ViewData["Title"] = "Resource Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Resource Management", Href = "", Icon = "fas fa-users-cog" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header Following Design Guidelines -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 shadow-sm dark:shadow-lg">
    <!-- Title and Actions Row -->
    <div class="flex items-center justify-between px-10 py-8 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-10">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight flex items-center">
                <i class="fas fa-users-cog mr-3 text-primary-600 dark:text-primary-400"></i>
                Resource Management
            </h1>
            <!-- Inline stats badges - Multiple lines when needed -->
            <div class="hidden md:flex flex-wrap items-center gap-x-4 gap-y-2 max-w-md">
                <span class="inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-users mr-2 text-blue-600 dark:text-blue-400"></i>@Model.Count() Total
                </span>
                <span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-xs font-medium">
                    <i class="fas fa-user-check mr-2 text-emerald-600 dark:text-emerald-400"></i>@Model.Count(r => r.IsActive) Active
                </span>
                <span class="inline-flex items-center px-3 py-1.5 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 rounded-full text-xs font-medium">
                    <i class="fas fa-user-tie mr-2 text-purple-600 dark:text-purple-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Human) People
                </span>
                <span class="inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 rounded-full text-xs font-medium">
                    <i class="fas fa-tools mr-2 text-amber-600 dark:text-amber-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Equipment) Equipment
                </span>
                @if (Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Material) > 0)
                {
                    <span class="inline-flex items-center px-3 py-1.5 bg-cyan-100 dark:bg-cyan-900/50 text-cyan-800 dark:text-cyan-200 rounded-full text-xs font-medium">
                        <i class="fas fa-boxes mr-2 text-cyan-600 dark:text-cyan-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Material) Materials
                    </span>
                }
                @if (Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Facility) > 0)
                {
                    <span class="inline-flex items-center px-3 py-1.5 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 rounded-full text-xs font-medium">
                        <i class="fas fa-building mr-2 text-indigo-600 dark:text-indigo-400"></i>@Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Facility) Facilities
                    </span>
                }
            </div>
        </div>

        <!-- Action Buttons - Multiple lines when needed -->
        <div class="flex flex-wrap items-center gap-x-5 gap-y-3 flex-shrink-0">
            @{
                ViewData["Text"] = "Add Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Schedule View";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-calendar";
                ViewData["Href"] = Url.Action("Schedule");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Capacity Planning";
                ViewData["Variant"] = "tertiary";
                ViewData["Icon"] = "fas fa-chart-bar";
                ViewData["Href"] = Url.Action("Capacity");
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- Additional actions can wrap to new line -->
            @{
                ViewData["Text"] = "Export";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-download";
                ViewData["Href"] = "#";
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>

    <!-- Description Row -->
    <div class="px-10 py-6">
        <p class="text-sm text-neutral-600 dark:text-neutral-300 ml-2">
            Manage team resources, allocations, and capacity planning with comprehensive tracking and analytics
        </p>
    </div>
</div>



<!-- Filters and Search Following Design Guidelines -->
@{
    ViewData["Title"] = "Filters & Search";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["BodyContent"] = @"
        <!-- Primary Filter Row -->
        <div class='mb-6'>
            <div class='grid grid-cols-1 lg:grid-cols-12 gap-6'>
                <!-- Search - Takes priority -->
                <div class='lg:col-span-5'>
                    <label for='searchInput' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Search Resources</label>
                    <div class='relative'>
                        <input type='text' id='searchInput' class='w-full pl-10 pr-4 py-2.5 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Search by name, department, skills...'>
                        <div class='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                            <i class='fas fa-search text-neutral-400 dark:text-neutral-500'></i>
                        </div>
                    </div>
                </div>

                <!-- Quick Filters - Multiple lines when needed -->
                <div class='lg:col-span-7'>
                    <label class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Quick Filters</label>
                    <div class='flex flex-wrap gap-3'>
                        <button type='button' class='quick-filter-btn px-4 py-2.5 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1' data-filter='active'>
                            <i class='fas fa-user-check mr-2 text-emerald-500'></i>Active
                        </button>
                        <button type='button' class='quick-filter-btn px-4 py-2.5 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1' data-filter='human'>
                            <i class='fas fa-user-tie mr-2 text-blue-500'></i>People
                        </button>
                        <button type='button' class='quick-filter-btn px-4 py-2.5 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1' data-filter='equipment'>
                            <i class='fas fa-tools mr-2 text-amber-500'></i>Equipment
                        </button>
                        <button type='button' class='quick-filter-btn px-4 py-2.5 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1' data-filter='available'>
                            <i class='fas fa-calendar-check mr-2 text-emerald-500'></i>Available
                        </button>
                        <button type='button' class='quick-filter-btn px-4 py-2.5 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1' data-filter='overallocated'>
                            <i class='fas fa-exclamation-triangle mr-2 text-red-500'></i>Over-allocated
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Filters Row - Multiple lines for organization -->
        <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-6'>
            <div>
                <label for='typeFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Type</label>
                <select id='typeFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Types</option>
                    <option value='Human'>Human</option>
                    <option value='Equipment'>Equipment</option>
                    <option value='Material'>Material</option>
                    <option value='Facility'>Facility</option>
                </select>
            </div>
            <div>
                <label for='statusFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Status</label>
                <select id='statusFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Statuses</option>
                    <option value='true'>Active</option>
                    <option value='false'>Inactive</option>
                </select>
            </div>
            <div>
                <label for='departmentFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Department</label>
                <select id='departmentFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Departments</option>
                </select>
            </div>
            <div>
                <label for='locationFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Location</label>
                <select id='locationFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Locations</option>
                </select>
            </div>
            <div>
                <label for='skillFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Skills</label>
                <select id='skillFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Skills</option>
                </select>
            </div>
            <div>
                <label for='availabilityFilter' class='block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2'>Availability</label>
                <select id='availabilityFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All</option>
                    <option value='available'>Available</option>
                    <option value='partial'>Partially Available</option>
                    <option value='busy'>Fully Allocated</option>
                </select>
            </div>
        </div>

        <!-- Filter Actions Row -->
        <div class='flex flex-wrap items-center justify-between gap-4'>
            <div class='flex flex-wrap items-center gap-3'>
                <button type='button' id='clearFilters' class='inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'>
                    <i class='fas fa-times mr-2'></i>Clear Filters
                </button>
                <button type='button' id='saveFilters' class='inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'>
                    <i class='fas fa-bookmark mr-2'></i>Save Filter Set
                </button>
            </div>
            <div class='text-sm text-neutral-600 dark:text-neutral-400'>
                <span id='filterResultCount'>@Model.Count()</span> resources match current filters
            </div>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- View Controls and Results Following Design Guidelines -->
@{
    ViewData["Title"] = "View & Sort Options";
    ViewData["Icon"] = "fas fa-eye";
    ViewData["BodyContent"] = @"
        <!-- View Controls Row -->
        <div class='flex flex-wrap items-center justify-between gap-6 mb-6'>
            <!-- Left: View Toggle and Results -->
            <div class='flex flex-wrap items-center gap-6'>
                <div class='flex items-center space-x-3'>
                    <span class='text-sm font-semibold text-neutral-700 dark:text-neutral-300'>View:</span>
                    <div class='flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1'>
                        <button type='button' id='gridView' class='px-4 py-2 text-sm font-medium rounded-md bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm'>
                            <i class='fas fa-th mr-2'></i>Grid
                        </button>
                        <button type='button' id='listView' class='px-4 py-2 text-sm font-medium rounded-md text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200'>
                            <i class='fas fa-list mr-2'></i>List
                        </button>
                        <button type='button' id='tableView' class='px-4 py-2 text-sm font-medium rounded-md text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200'>
                            <i class='fas fa-table mr-2'></i>Table
                        </button>
                    </div>
                </div>

                <div class='text-sm text-neutral-600 dark:text-neutral-400'>
                    <span id='resourceCount'>@Model.Count()</span> resources found
                </div>
            </div>

            <!-- Right: Sort and Display Options - Multiple lines when needed -->
            <div class='flex flex-wrap items-center gap-6'>
                <div class='flex items-center space-x-3'>
                    <label for='sortBy' class='text-sm font-medium text-neutral-700 dark:text-neutral-300'>Sort by:</label>
                    <select id='sortBy' class='px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='name'>Name</option>
                        <option value='type'>Type</option>
                        <option value='department'>Department</option>
                        <option value='rate'>Hourly Rate</option>
                        <option value='capacity'>Capacity</option>
                        <option value='updated'>Last Updated</option>
                    </select>
                </div>

                <div class='flex items-center space-x-3'>
                    <span class='text-sm font-medium text-neutral-700 dark:text-neutral-300'>Display:</span>
                    <div class='flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1'>
                        <button type='button' id='compactView' class='px-3 py-1.5 text-xs font-medium rounded text-neutral-600 dark:text-neutral-400 hover:bg-white dark:hover:bg-neutral-700' title='Compact'>
                            <i class='fas fa-compress-alt mr-1'></i>Compact
                        </button>
                        <button type='button' id='comfortableView' class='px-3 py-1.5 text-xs font-medium rounded bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm' title='Comfortable'>
                            <i class='fas fa-expand-alt mr-1'></i>Comfortable
                        </button>
                    </div>
                </div>

                <div class='flex items-center space-x-3'>
                    <span class='text-sm font-medium text-neutral-700 dark:text-neutral-300'>Per Page:</span>
                    <select id='itemsPerPage' class='px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='12'>12</option>
                        <option value='24' selected>24</option>
                        <option value='48'>48</option>
                        <option value='96'>96</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Active Filters Display - Multiple lines for many filters -->
        <div id='activeFilters' class='hidden border-t border-neutral-200 dark:border-neutral-700 pt-6'>
            <div class='flex flex-wrap items-start gap-3'>
                <span class='text-sm font-medium text-neutral-700 dark:text-neutral-300 mt-1'>Active filters:</span>
                <div id='activeFiltersList' class='flex flex-wrap gap-2'>
                    <!-- Active filter tags will be inserted here by JavaScript -->
                </div>
            </div>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Resource List -->
<div class="px-4">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="resourceContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var resource in Model)
        {
            <div class="resource-card transition-all duration-200 hover:shadow-md dark:hover:shadow-lg hover:-translate-y-0.5"
                 data-type="@resource.Type.ToString().ToLower()"
                 data-status="@resource.IsActive.ToString().ToLower()"
                 data-department="@(resource.Department ?? "")"
                 data-location="@(resource.Location ?? "")">
                <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm dark:shadow-lg">
                    <div class="px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6">
                                <div class="w-12 h-12 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                                    <i class="@GetResourceTypeIcon(resource.Type) text-white text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@resource.Name</h3>
                                    <p class="text-sm text-neutral-600 dark:text-neutral-300 mt-1">@resource.Type</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                @if (resource.IsActive)
                                {
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200">
                                        <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200">
                                        <i class="fas fa-times-circle mr-2 text-neutral-600 dark:text-neutral-400"></i>Inactive
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="px-8 py-6 space-y-6">
                        @if (!string.IsNullOrEmpty(resource.Description))
                        {
                            <p class="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">@resource.Description</p>
                        }

                        <!-- Resource Details -->
                        <div class="space-y-4">
                            @if (!string.IsNullOrEmpty(resource.Department))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-building w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Department</span>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(resource.Location))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-map-marker-alt w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Location</span>
                                </div>
                            }
                            @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human)
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                    <i class="fas fa-clock w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                    <span>@resource.Capacity hrs/day</span>
                                </div>
                                @if (resource.HourlyRate > 0)
                                {
                                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                                        <i class="fas fa-dollar-sign w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                                        <span>@resource.HourlyRate.ToString("C")/hr</span>
                                    </div>
                                }
                            }
                        </div>

                        <!-- Skills (for Human resources) -->
                        @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human && !string.IsNullOrEmpty(resource.Skills))
                        {
                            <div>
                                <h4 class="text-xs font-semibold text-neutral-500 dark:text-neutral-400 mb-3">Skills</h4>
                                <div class="flex flex-wrap gap-2">
                                    @{
                                        var skills = resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries);
                                        var displaySkills = skills.Take(3);
                                        var remainingCount = skills.Length - 3;
                                    }
                                    @foreach (var skill in displaySkills)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                                            @skill.Trim()
                                        </span>
                                    }
                                    @if (remainingCount > 0)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300">
                                            +@remainingCount more
                                        </span>
                                    }
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Card Footer with Actions -->
                    <div class="px-8 py-6 bg-neutral-50 dark:bg-neutral-700/50 border-t border-neutral-200 dark:border-neutral-700 rounded-b-xl">
                        <div class="flex items-center justify-between">
                            <small class="text-neutral-500 dark:text-neutral-400">
                                Updated @(resource.UpdatedAt?.ToString("MMM dd") ?? "N/A")
                            </small>
                            <div class="flex items-center space-x-2">
                                <a asp-action="Details" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 hover:border-blue-300 dark:hover:border-blue-600 rounded-lg transition-all duration-200"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 hover:text-amber-600 dark:hover:text-amber-400 hover:border-amber-300 dark:hover:border-amber-600 rounded-lg transition-all duration-200"
                                   title="Edit Resource">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Allocations" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 hover:border-purple-300 dark:hover:border-purple-600 rounded-lg transition-all duration-200"
                                   title="View Allocations">
                                    <i class="fas fa-calendar-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full text-center py-20">
            <div class="w-20 h-20 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-users-cog text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-3">No Resources Found</h3>
            <p class="text-neutral-600 dark:text-neutral-400 mb-8 max-w-md mx-auto">
                Start building your team by adding resources like team members, equipment, and facilities to your project.
            </p>
            @{
                ViewData["Text"] = "Add Your First Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
                ViewData["Size"] = "large";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
    </div>
</div>

@section Styles {
    <partial name="_IndexStyles" />
}

@section Scripts {
    <partial name="_IndexScripts" />
}



@functions {
    string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-question"
        };
    }

    string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-primary-500 to-primary-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-warning-500 to-warning-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-info-500 to-info-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-success-500 to-success-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
