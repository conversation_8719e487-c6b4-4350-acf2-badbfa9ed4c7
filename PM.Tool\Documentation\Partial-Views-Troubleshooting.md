# Partial Views Implementation - Troubleshooting Guide

## Overview
This guide helps resolve common issues when implementing the partial view separation pattern in PM.Tool following the UI Design System v2.1 guidelines.

## Common Issues and Solutions

### 1. "Section 'Scripts' is already defined" Error

**Problem**: Multiple `@section Scripts` blocks in the same view.

**Cause**: When migrating to partial views, old script sections may remain in the main view file.

**Solution**:
```csharp
// ❌ WRONG - Multiple sections
@section Scripts {
    <partial name="_ViewNameScripts" />
}

@section Scripts {
    <script>
        // Old inline scripts
    </script>
}

// ✅ CORRECT - Single section with partial
@section Scripts {
    <partial name="_ViewNameScripts" />
}
```

**Steps to Fix**:
1. Search for all `@section Scripts` in the view file
2. Remove duplicate sections
3. Keep only the partial reference
4. Move any inline scripts to the scripts partial

### 2. "Section 'Styles' is already defined" Error

**Problem**: Similar to scripts, multiple style sections exist.

**Solution**:
```csharp
// ❌ WRONG - Multiple style sections
@section Styles {
    <partial name="_ViewNameStyles" />
}

<style>
    /* Inline styles */
</style>

// ✅ CORRECT - Single section with partial
@section Styles {
    <partial name="_ViewNameStyles" />
}
```

### 3. Missing CSS Classes or Styles

**Problem**: Styles not applying after migration to partials.

**Cause**: Inline styles not moved to the styles partial.

**Solution**:
1. Find all `<style>` blocks in the main view
2. Move content to `_ViewNameStyles.cshtml`
3. Remove inline style blocks from main view

### 4. JavaScript Functions Not Found

**Problem**: JavaScript functions throwing "not defined" errors.

**Cause**: Scripts not properly organized in partials.

**Solution**:
1. Ensure all JavaScript is in the scripts partial
2. Use proper IIFE pattern:
```javascript
(function() {
    'use strict';
    
    // Your code here
    
})();
```

### 5. jQuery/$ Not Defined

**Problem**: Using jQuery in partials before it's loaded.

**Solution**:
```javascript
// ❌ WRONG - Direct jQuery usage
$(document).ready(function() {
    // Code here
});

// ✅ CORRECT - Check for jQuery first
document.addEventListener('DOMContentLoaded', () => {
    if (typeof $ !== 'undefined') {
        // jQuery code here
    } else {
        // Vanilla JavaScript fallback
    }
});
```

### 6. Partial View Not Found

**Problem**: `InvalidOperationException: The partial view '_ViewNameStyles' was not found.`

**Cause**: Incorrect partial view path or naming.

**Solution**:
1. Ensure partial files are in the correct location:
   ```
   Views/
   ├── ControllerName/
   │   ├── ViewName.cshtml
   │   ├── _ViewNameStyles.cshtml
   │   └── _ViewNameScripts.cshtml
   ```

2. Check naming convention:
   - Main view: `ViewName.cshtml`
   - Styles: `_ViewNameStyles.cshtml`
   - Scripts: `_ViewNameScripts.cshtml`

### 7. Theme Variables Not Working

**Problem**: CSS custom properties not applying correctly.

**Solution**:
```css
/* ❌ WRONG - Hardcoded colors */
.component {
    background: #ffffff;
    color: #000000;
}

/* ✅ CORRECT - Theme-aware variables */
.component {
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #000000);
}

/* ✅ BETTER - Tailwind classes */
.component {
    @apply bg-white dark:bg-neutral-800 text-neutral-900 dark:text-white;
}
```

### 8. Accessibility Issues

**Problem**: ARIA attributes or keyboard navigation not working.

**Solution**:
1. Ensure proper ARIA setup in scripts partial:
```javascript
setupAccessibility() {
    // Add ARIA labels
    const elements = document.querySelectorAll('.interactive-element');
    elements.forEach((el, index) => {
        el.setAttribute('role', 'button');
        el.setAttribute('aria-label', `Action ${index + 1}`);
        el.setAttribute('tabindex', '0');
    });
    
    // Add live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.className = 'sr-only';
    document.body.appendChild(liveRegion);
}
```

## Best Practices

### 1. File Organization
```
Views/Resource/
├── Index.cshtml              # Main markup only
├── _IndexStyles.cshtml       # Page-specific styles
├── _IndexScripts.cshtml      # Page-specific JavaScript
├── Details.cshtml            # Main markup only
├── _DetailsStyles.cshtml     # Page-specific styles
└── _DetailsScripts.cshtml    # Page-specific JavaScript
```

### 2. Main View Structure
```html
@{
    ViewData["Title"] = "Page Title";
}

<!-- Main content markup only -->
<div class="page-container">
    <!-- Page content here -->
</div>

@section Styles {
    <partial name="_PageNameStyles" />
}

@section Scripts {
    <partial name="_PageNameScripts" />
}
```

### 3. Styles Partial Structure
```html
<style>
    /* Page-specific styles following design system */
    
    /* 1. Component-specific styles */
    .page-component {
        @apply px-8 py-6 bg-white dark:bg-neutral-800;
    }
    
    /* 2. Responsive adjustments */
    @media (max-width: 768px) {
        .mobile-specific {
            /* Mobile styles */
        }
    }
    
    /* 3. Theme-specific overrides */
    .dark .dark-specific {
        /* Dark theme styles */
    }
    
    /* 4. Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .animated-element {
            transition: none;
            animation: none;
        }
    }
</style>
```

### 4. Scripts Partial Structure
```html
<script>
    (function() {
        'use strict';
        
        // Configuration
        const CONFIG = {
            apiEndpoint: '@Url.Action("Action")',
            debounceDelay: 300
        };
        
        // Class-based organization
        class PageManager {
            constructor() {
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.setupThemeHandling();
                this.setupAccessibility();
            }
            
            bindEvents() {
                // Event binding
            }
            
            setupThemeHandling() {
                // Theme-aware functionality
            }
            
            setupAccessibility() {
                // Accessibility features
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            new PageManager();
        });
        
    })();
</script>
```

## Migration Checklist

When converting existing views to partial view pattern:

- [ ] Create `_ViewNameStyles.cshtml` file
- [ ] Create `_ViewNameScripts.cshtml` file
- [ ] Move all `<style>` blocks to styles partial
- [ ] Move all `<script>` blocks to scripts partial
- [ ] Remove duplicate `@section` declarations
- [ ] Add partial references to main view
- [ ] Test theme switching functionality
- [ ] Verify responsive behavior
- [ ] Check accessibility compliance
- [ ] Validate JavaScript functionality
- [ ] Test in both light and dark themes

## Performance Optimization

### 1. Conditional Loading
```csharp
@if (ViewBag.LoadAdvancedFeatures == true) {
    @section Scripts {
        <partial name="_AdvancedScripts" />
    }
}
```

### 2. Minification Ready
Organize code for easy minification:
- Use consistent formatting
- Avoid inline styles/scripts
- Group related functionality

### 3. Caching Strategy
```csharp
@{
    var cacheKey = $"styles_{ViewContext.RouteData.Values["action"]}_{DateTime.Now:yyyyMMdd}";
}

@section Styles {
    <partial name="_ViewNameStyles" />
}
```

## Debugging Tips

1. **Use Browser DevTools**: Check if styles/scripts are loading
2. **Console Errors**: Look for JavaScript errors in browser console
3. **Network Tab**: Verify partial views are being requested
4. **View Source**: Ensure sections are rendered correctly
5. **Theme Testing**: Test both light and dark themes
6. **Responsive Testing**: Check mobile and desktop layouts

## Support

For additional help:
- Review the UI Design System v2.1 documentation
- Check existing implementations in Resource Management views
- Follow the established patterns and conventions
- Test thoroughly in all supported browsers and themes
