@model IEnumerable<PM.Tool.Core.Entities.Meeting>
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Meeting Management";

    // Calculate stats for header badges
    var totalMeetings = Model?.Count() ?? 0;
    var todayMeetings = Model?.Where(m => m.ScheduledDate.Date == DateTime.Today).Count() ?? 0;
    var upcomingMeetings = Model?.Where(m => m.ScheduledDate > DateTime.Now && m.Status == MeetingStatus.Scheduled).Count() ?? 0;
    var inProgressMeetings = Model?.Where(m => m.Status == MeetingStatus.InProgress).Count() ?? 0;
    var completedMeetings = Model?.Where(m => m.Status == MeetingStatus.Completed).Count() ?? 0;
    var completionRate = totalMeetings > 0 ? Math.Round((double)completedMeetings / totalMeetings * 100, 1) : 0;
}

@section Styles {
    <partial name="Partials/_MeetingStyles" />
}

<!-- Refined Page Header -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <div class="px-10 py-8 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-10 flex-1 min-w-0 pr-10">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight ml-2">Meeting Management</h1>
                <!-- Compact Analytics Badges -->
                <div class="hidden md:flex items-center space-x-5">
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                        <i class="fas fa-users mr-2 text-blue-600 dark:text-blue-400"></i>
                        @totalMeetings Total
                    </span>
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 rounded-full text-xs font-medium">
                        <i class="fas fa-calendar-day mr-2 text-amber-600 dark:text-amber-400"></i>
                        @todayMeetings Today
                    </span>
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 rounded-full text-xs font-medium">
                        <i class="fas fa-clock mr-2 text-indigo-600 dark:text-indigo-400"></i>
                        @upcomingMeetings Upcoming
                    </span>
                    @if (inProgressMeetings > 0)
                    {
                        <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 rounded-full text-xs font-medium">
                            <i class="fas fa-play mr-2 text-green-600 dark:text-green-400"></i>
                            @inProgressMeetings Active
                        </span>
                    }
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-xs font-medium">
                        <i class="fas fa-chart-line mr-2 text-emerald-600 dark:text-emerald-400"></i>
                        @completionRate% Complete
                    </span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-5 flex-shrink-0">
                <a href="@Url.Action("Create")" class="btn-primary-enhanced btn-medium-enhanced inline-flex items-center text-sm text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-2.5"></i>Schedule Meeting
                </a>
                <a href="@Url.Action("Calendar")" class="btn-secondary-enhanced btn-medium-enhanced inline-flex items-center text-sm rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <i class="fas fa-calendar mr-2.5"></i>Calendar View
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Refined Filter Bar -->
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-10">
    <div class="px-10 py-8">
        <form>
            <div class="flex flex-wrap items-center gap-8">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-4">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-4 ml-2">Quick:</span>
                <div class="flex items-center gap-3">
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="today">
                        <i class="fas fa-calendar-day mr-2 text-blue-500"></i>Today
                    </button>
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="upcoming">
                        <i class="fas fa-clock mr-2 text-indigo-500"></i>Upcoming
                    </button>
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="active">
                        <i class="fas fa-play mr-2 text-green-500"></i>Active
                    </button>
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="completed">
                        <i class="fas fa-check mr-2 text-emerald-500"></i>Completed
                    </button>
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="mine">
                        <i class="fas fa-user mr-2 text-purple-500"></i>My Meetings
                    </button>
                </div>
            </div>

            <!-- Separator -->
            <div class="h-8 w-px bg-neutral-300 dark:bg-neutral-600"></div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-6 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           id="searchInput"
                           placeholder="Search meetings..."
                           class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-3">
                    <!-- Status Filter -->
                    <select id="statusFilter" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[110px]">
                        <option value="">All Status</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="inprogress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>

                    <!-- Type Filter -->
                    <select id="typeFilter" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[110px]">
                        <option value="">All Types</option>
                        <option value="standup">Stand-up</option>
                        <option value="planning">Planning</option>
                        <option value="review">Review</option>
                        <option value="retrospective">Retrospective</option>
                        <option value="general">General</option>
                    </select>

                    <!-- Date Range Filter -->
                    <select id="dateFilter" class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Dates</option>
                        <option value="today">Today</option>
                        <option value="tomorrow">Tomorrow</option>
                        <option value="thisweek">This Week</option>
                        <option value="nextweek">Next Week</option>
                        <option value="thismonth">This Month</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-5 ml-auto">
                    <button type="button" id="applyFilters" class="btn-primary-enhanced btn-medium-enhanced inline-flex items-center text-sm text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        <i class="fas fa-filter mr-2.5"></i>Apply
                    </button>
                    <button type="button" id="clearFilters" class="btn-tertiary-enhanced btn-medium-enhanced inline-flex items-center text-sm rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        <i class="fas fa-times mr-2"></i>Clear
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Refined View Controls -->
<div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 mb-8">
    <div class="px-10 py-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 ml-2">View:</span>
                <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
                    <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn active bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm" data-view="grid">
                        <i class="fas fa-th mr-2"></i>Grid
                    </button>
                    <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="list">
                        <i class="fas fa-list mr-2"></i>List
                    </button>
                    <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="calendar">
                        <i class="fas fa-calendar mr-2"></i>Calendar
                    </button>
                </div>
            </div>
            <div class="flex items-center space-x-8">
                <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Showing <span id="visibleCount">@totalMeetings</span> of @totalMeetings meetings
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Meetings Content Container -->
<div class="px-10">
    <div class="space-y-8">
    <!-- Grid View -->
    <div id="gridView" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 ml-2" style="display: block;">
        @if (Model != null && Model.Any())
        {
            @foreach (var meeting in Model)
            {
                <div class="meeting-card transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
                     data-status="@meeting.Status.ToString().ToLower()"
                     data-type="@meeting.Type.ToString().ToLower()"
                     data-date="@meeting.ScheduledDate.ToString("yyyy-MM-dd")"
                     data-search="@($"{meeting.Title} {meeting.Description} {meeting.Location}".ToLower())">
                    <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm h-full overflow-hidden">
                        <!-- Card Header -->
                        <div class="px-6 py-4 border-b border-neutral-200 dark:border-neutral-700">
                            <div class="flex justify-between items-start">
                                <div class="flex items-center space-x-3">
                                    @{
                                        var (statusIcon, statusColor) = meeting.Status.ToString().ToLower() switch {
                                            "scheduled" => ("fas fa-calendar-check", "blue"),
                                            "inprogress" => ("fas fa-play-circle", "green"),
                                            "completed" => ("fas fa-check-circle", "emerald"),
                                            "cancelled" => ("fas fa-times-circle", "red"),
                                            _ => ("fas fa-circle", "gray")
                                        };

                                        var (typeIcon, typeColor) = meeting.Type.ToString().ToLower() switch {
                                            "standup" => ("fas fa-users", "indigo"),
                                            "planning" => ("fas fa-tasks", "purple"),
                                            "review" => ("fas fa-search", "amber"),
                                            "retrospective" => ("fas fa-history", "pink"),
                                            _ => ("fas fa-comments", "gray")
                                        };
                                    }
                                    <div class="w-10 h-10 bg-@(statusColor)-100 dark:bg-@(statusColor)-900/30 rounded-lg flex items-center justify-center">
                                        <i class="@statusIcon text-@(statusColor)-600 dark:text-@(statusColor)-400"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@meeting.Title</h3>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <span class="inline-flex items-center px-2 py-0.5 bg-@(statusColor)-100 dark:bg-@(statusColor)-900/50 text-@(statusColor)-800 dark:text-@(statusColor)-200 rounded text-xs font-medium">
                                                @meeting.Status
                                            </span>
                                            <span class="inline-flex items-center px-2 py-0.5 bg-@(typeColor)-100 dark:bg-@(typeColor)-900/50 text-@(typeColor)-800 dark:text-@(typeColor)-200 rounded text-xs font-medium">
                                                <i class="@typeIcon mr-1"></i>@meeting.Type
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="flex items-center space-x-1">
                                    @if (meeting.Status == MeetingStatus.Scheduled)
                                    {
                                        <button onclick="startMeeting(@meeting.Id)" class="btn-icon-enhanced bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50" title="Start Meeting">
                                            <i class="fas fa-play text-sm"></i>
                                        </button>
                                    }
                                    <a href="@Url.Action("Edit", new { id = meeting.Id })" class="btn-icon-enhanced bg-neutral-100 text-neutral-600 hover:bg-neutral-200 dark:bg-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-600" title="Edit">
                                        <i class="fas fa-edit text-sm"></i>
                                    </a>
                                    <a href="@Url.Action("Details", new { id = meeting.Id })" class="btn-icon-enhanced bg-primary-100 text-primary-600 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-400 dark:hover:bg-primary-900/50" title="View Details">
                                        <i class="fas fa-eye text-sm"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Card Body -->
                        <div class="px-6 py-4">
                            @if (!string.IsNullOrEmpty(meeting.Description))
                            {
                                <p class="text-sm text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-2">@meeting.Description</p>
                            }

                            <div class="space-y-3">
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                                    <i class="fas fa-calendar mr-3 w-4 text-neutral-400"></i>
                                    <span>@meeting.ScheduledDate.ToString("MMM dd, yyyy")</span>
                                </div>
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                                    <i class="fas fa-clock mr-3 w-4 text-neutral-400"></i>
                                    <span>@meeting.ScheduledDate.ToString("HH:mm") - @meeting.ScheduledDate.AddMinutes(meeting.DurationMinutes).ToString("HH:mm")</span>
                                    <span class="ml-2 text-xs text-neutral-500">(@meeting.DurationMinutes min)</span>
                                </div>
                                @if (!string.IsNullOrEmpty(meeting.Location))
                                {
                                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                                        <i class="fas fa-map-marker-alt mr-3 w-4 text-neutral-400"></i>
                                        <span class="truncate">@meeting.Location</span>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(meeting.MeetingLink))
                                {
                                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                                        <i class="fas fa-video mr-3 w-4 text-neutral-400"></i>
                                        <a href="@meeting.MeetingLink" target="_blank" class="text-primary-600 hover:text-primary-700 truncate">Join Online</a>
                                    </div>
                                }
                                <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                                    <i class="fas fa-users mr-3 w-4 text-neutral-400"></i>
                                    <span>@meeting.Attendees.Count() participants</span>
                                </div>
                            </div>
                        </div>

                        <!-- Card Footer -->
                        <div class="px-6 py-3 bg-neutral-50 dark:bg-neutral-700/50 border-t border-neutral-200 dark:border-neutral-700">
                            <div class="flex justify-between items-center text-xs text-neutral-500 dark:text-neutral-400">
                                <span>Created @meeting.CreatedAt.ToString("MMM dd")</span>
                                @if (meeting.ScheduledDate < DateTime.Now && meeting.Status == MeetingStatus.Scheduled)
                                {
                                    <span class="text-red-600 dark:text-red-400 font-medium">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>Overdue
                                    </span>
                                }
                                else if (meeting.ScheduledDate.Date == DateTime.Today)
                                {
                                    <span class="text-amber-600 dark:text-amber-400 font-medium">
                                        <i class="fas fa-clock mr-1"></i>Today
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-span-full">
                <div class="text-center py-16">
                    <div class="w-20 h-20 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-users text-3xl text-neutral-400 dark:text-neutral-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-3">No meetings found</h3>
                    <p class="text-neutral-600 dark:text-neutral-400 mb-8 max-w-md mx-auto">
                        Get started by scheduling your first meeting or adjust your filters to see existing meetings.
                    </p>
                    <div class="flex items-center justify-center space-x-6">
                        <a href="@Url.Action("Create")" class="btn-primary-enhanced btn-large-enhanced inline-flex items-center text-base text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-3"></i>Schedule Meeting
                        </a>
                        <button type="button" onclick="clearAllFilters()" class="btn-secondary-enhanced btn-medium-enhanced inline-flex items-center text-sm rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <i class="fas fa-filter mr-2.5"></i>Clear Filters
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- List View -->
    <div id="listView" class="space-y-6 ml-2" style="display: none;">
        @if (Model != null && Model.Any())
        {
            @foreach (var meeting in Model)
            {
                <div class="meeting-list-item bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                     data-status="@meeting.Status.ToString().ToLower()"
                     data-type="@meeting.Type.ToString().ToLower()"
                     data-date="@meeting.ScheduledDate.ToString("yyyy-MM-dd")"
                     data-search="@($"{meeting.Title} {meeting.Description} {meeting.Location}".ToLower())">
                    <div class="px-8 py-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-6 flex-1">
                                @{
                                    var (statusIcon, statusColor) = meeting.Status.ToString().ToLower() switch {
                                        "scheduled" => ("fas fa-calendar-check", "blue"),
                                        "inprogress" => ("fas fa-play-circle", "green"),
                                        "completed" => ("fas fa-check-circle", "emerald"),
                                        "cancelled" => ("fas fa-times-circle", "red"),
                                        _ => ("fas fa-circle", "gray")
                                    };
                                }
                                <div class="w-8 h-8 bg-@(statusColor)-100 dark:bg-@(statusColor)-900/30 rounded-lg flex items-center justify-center">
                                    <i class="@statusIcon text-@(statusColor)-600 dark:text-@(statusColor)-400 text-sm"></i>
                                </div>

                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-3">
                                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white truncate">@meeting.Title</h3>
                                        <span class="inline-flex items-center px-2 py-0.5 bg-@(statusColor)-100 dark:bg-@(statusColor)-900/50 text-@(statusColor)-800 dark:text-@(statusColor)-200 rounded text-xs font-medium">
                                            @meeting.Status
                                        </span>
                                        <span class="inline-flex items-center px-2 py-0.5 bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200 rounded text-xs font-medium">
                                            @meeting.Type
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-6 mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                                        <span class="flex items-center">
                                            <i class="fas fa-calendar mr-2"></i>
                                            @meeting.ScheduledDate.ToString("MMM dd, yyyy")
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-clock mr-2"></i>
                                            @meeting.ScheduledDate.ToString("HH:mm") - @meeting.ScheduledDate.AddMinutes(meeting.DurationMinutes).ToString("HH:mm")
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-users mr-2"></i>
                                            @meeting.Attendees.Count() participants
                                        </span>
                                        @if (!string.IsNullOrEmpty(meeting.Location))
                                        {
                                            <span class="flex items-center">
                                                <i class="fas fa-map-marker-alt mr-2"></i>
                                                @meeting.Location
                                            </span>
                                        }
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-4">
                                @if (meeting.Status == MeetingStatus.Scheduled)
                                {
                                    <button onclick="startMeeting(@meeting.Id)" class="btn-success-enhanced btn-small-enhanced inline-flex items-center text-white rounded-lg focus:ring-2 focus:ring-green-500 focus:ring-offset-1">
                                        <i class="fas fa-play mr-2"></i>Start
                                    </button>
                                }
                                <a href="@Url.Action("Edit", new { id = meeting.Id })" class="btn-secondary-enhanced btn-small-enhanced inline-flex items-center rounded-lg focus:ring-2 focus:ring-neutral-500 focus:ring-offset-1">
                                    <i class="fas fa-edit mr-2"></i>Edit
                                </a>
                                <a href="@Url.Action("Details", new { id = meeting.Id })" class="btn-primary-enhanced btn-small-enhanced inline-flex items-center text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-1">
                                    <i class="fas fa-eye mr-2"></i>View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="text-center py-16">
                <div class="w-20 h-20 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-list text-3xl text-neutral-400 dark:text-neutral-500"></i>
                </div>
                <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-3">No meetings in list view</h3>
                <p class="text-neutral-600 dark:text-neutral-400 mb-8">Switch to grid view or schedule a new meeting to get started.</p>
            </div>
        }
    </div>

    <!-- Calendar View Placeholder -->
    <div id="calendarView" class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl p-8" style="display: none;">
        <div class="text-center">
            <div class="w-20 h-20 bg-neutral-100 dark:bg-neutral-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-calendar text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-3">Calendar View</h3>
            <p class="text-neutral-600 dark:text-neutral-400 mb-6">Calendar integration will be implemented here.</p>
            <a href="@Url.Action("Calendar")" class="btn-primary-enhanced btn-medium-enhanced inline-flex items-center text-sm text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                <i class="fas fa-external-link-alt mr-2.5"></i>Open Full Calendar
            </a>
        </div>
    </div>
    </div> <!-- Close space-y-6 div -->
</div> <!-- Close px-8 content container -->

@section Scripts {
    <partial name="Partials/_MeetingScripts" />
}
