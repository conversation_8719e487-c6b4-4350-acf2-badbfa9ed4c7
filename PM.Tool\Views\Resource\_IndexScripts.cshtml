<script>
    // Resource Index Page Scripts - Following PM.Tool UI Design System v2.1

    (function() {
        'use strict';

        // Configuration and constants
        const CONFIG = {
            debounceDelay: 300,
            animationDuration: 200,
            pageSize: 20,
            apiEndpoint: '@Url.Action("Index", "Resource")',
            storageKey: 'resource-index-preferences'
        };

        // Enhanced Resource Index Manager
        class ResourceIndexManager {
            constructor() {
                this.currentView = 'grid';
                this.currentSort = 'name';
                this.filters = {
                    search: '',
                    type: '',
                    status: '',
                    department: '',
                    location: ''
                };
                this.resources = [];
                this.filteredResources = [];
                this.preferences = this.loadPreferences();

                this.init();
            }

            init() {
                try {
                    console.log('Initializing ResourceIndexManager components...');
                    this.bindEvents();
                    this.loadResources();
                    this.populateFilterDropdowns();
                    this.setupQuickFilters();
                    this.setupViewControls();
                    this.setupSorting();
                    this.setupThemeHandling();
                    this.setupAccessibility();
                    this.setupMultiLineLayouts();
                    this.restorePreferences();
                    console.log('ResourceIndexManager initialization complete');
                } catch (error) {
                    console.error('Error during ResourceIndexManager initialization:', error);
                    throw error;
                }
            }

            bindEvents() {
                // Search input with enhanced debounce
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    let searchTimeout;
                    searchInput.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.filters.search = e.target.value.toLowerCase();
                            this.applyFilters();
                            this.savePreferences();
                        }, CONFIG.debounceDelay);
                    });

                    // Add search clear functionality
                    searchInput.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape') {
                            e.target.value = '';
                            this.filters.search = '';
                            this.applyFilters();
                        }
                    });
                }

                // Enhanced filter dropdowns
                [
                    'typeFilter', 'statusFilter', 'departmentFilter', 'locationFilter',
                    'skillFilter', 'availabilityFilter', 'experienceFilter', 'costCenterFilter',
                    'lastActiveFilter', 'certificationFilter', 'projectCountFilter'
                ].forEach(filterId => {
                    const filter = document.getElementById(filterId);
                    if (filter) {
                        filter.addEventListener('change', (e) => {
                            const filterKey = filterId.replace('Filter', '');
                            this.filters[filterKey] = e.target.value;
                            this.applyFilters();
                            this.savePreferences();
                        });
                    }
                });

                // Range inputs for hourly rate
                const minRateFilter = document.getElementById('minRateFilter');
                const maxRateFilter = document.getElementById('maxRateFilter');

                if (minRateFilter) {
                    minRateFilter.addEventListener('input', (e) => {
                        this.filters.minRate = e.target.value;
                        this.applyFilters();
                        this.savePreferences();
                    });
                }

                if (maxRateFilter) {
                    maxRateFilter.addEventListener('input', (e) => {
                        this.filters.maxRate = e.target.value;
                        this.applyFilters();
                        this.savePreferences();
                    });
                }

                // Clear filters with animation
                const clearFilters = document.getElementById('clearFilters');
                if (clearFilters) {
                    clearFilters.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.clearAllFilters();
                    });
                }

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch (e.key) {
                            case 'f':
                                e.preventDefault();
                                searchInput?.focus();
                                break;
                            case 'k':
                                e.preventDefault();
                                this.clearAllFilters();
                                break;
                        }
                    }
                });
            }

            setupQuickFilters() {
                const quickFilterButtons = document.querySelectorAll('.quick-filter-btn');
                quickFilterButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();

                        const isActive = button.classList.contains('active');

                        // Remove active state from all buttons with animation
                        quickFilterButtons.forEach(btn => {
                            btn.classList.remove('active');
                            btn.style.transform = '';
                        });

                        if (!isActive) {
                            button.classList.add('active');
                            button.style.transform = 'scale(0.95)';
                            setTimeout(() => {
                                button.style.transform = '';
                            }, 150);

                            const filterType = button.dataset.filter;
                            this.applyQuickFilter(filterType);
                        } else {
                            this.clearAllFilters();
                        }
                    });

                    // Add hover effects
                    button.addEventListener('mouseenter', () => {
                        if (!button.classList.contains('active')) {
                            button.style.transform = 'translateY(-2px)';
                        }
                    });

                    button.addEventListener('mouseleave', () => {
                        if (!button.classList.contains('active')) {
                            button.style.transform = '';
                        }
                    });
                });
            }

            setupViewControls() {
                const viewButtons = document.querySelectorAll('#gridView, #listView, #tableView');

                viewButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Update active state with smooth transition
                        this.updateViewButtonStates(button, viewButtons);

                        // Update view with transition
                        const newView = button.id.replace('View', '');
                        this.switchView(newView);

                        this.savePreferences();
                    });
                });

                // Set initial state
                this.updateViewButtonStates(document.getElementById(`${this.currentView}View`), viewButtons);
            }

            updateViewButtonStates(activeButton, allButtons) {
                // Remove active state from all buttons
                allButtons.forEach(btn => {
                    btn.classList.remove('bg-white', 'dark:bg-neutral-700', 'text-primary-600',
                                        'dark:text-primary-400', 'shadow-sm');
                    btn.classList.add('text-neutral-600', 'dark:text-neutral-400');
                    btn.classList.remove('active');
                });

                // Add active state to clicked button
                if (activeButton) {
                    activeButton.classList.add('bg-white', 'dark:bg-neutral-700', 'text-primary-600',
                                               'dark:text-primary-400', 'shadow-sm');
                    activeButton.classList.remove('text-neutral-600', 'dark:text-neutral-400');
                    activeButton.classList.add('active');
                }
            }

            switchView(newView) {
                if (this.currentView === newView) return;

                const container = document.getElementById('resourceContainer');
                if (!container) return;

                // Add transition class
                container.style.transition = 'all 0.3s ease';
                container.style.opacity = '0.7';

                // Update view after short delay for smooth transition
                setTimeout(() => {
                    this.currentView = newView;
                    this.updateView();
                    this.updateResourceDisplay();

                    // Restore opacity
                    container.style.opacity = '1';

                    // Remove transition after animation
                    setTimeout(() => {
                        container.style.transition = '';
                    }, 300);
                }, 150);
            }

            setupSorting() {
                // Sort dropdown with persistence
                const sortBy = document.getElementById('sortBy');
                if (sortBy) {
                    sortBy.addEventListener('change', (e) => {
                        this.currentSort = e.target.value;
                        this.applySorting();
                        this.savePreferences();
                    });
                }

                // Initial sort
                this.applySorting();
            }

            setupThemeHandling() {
                // Listen for theme changes
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            this.handleThemeChange();
                        }
                    });
                });

                observer.observe(document.documentElement, {
                    attributes: true,
                    attributeFilter: ['class']
                });
            }

            setupAccessibility() {
                // Add ARIA labels and keyboard navigation
                const resourceCards = document.querySelectorAll('.resource-card');
                resourceCards.forEach((card, index) => {
                    card.setAttribute('role', 'article');
                    card.setAttribute('aria-label', `Resource ${index + 1}`);
                    card.setAttribute('tabindex', '0');

                    // Keyboard navigation for cards
                    card.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const detailsLink = card.querySelector('a[title="View Details"]');
                            if (detailsLink) {
                                detailsLink.click();
                            }
                        }
                    });
                });

                // Announce filter changes to screen readers
                const announcer = document.createElement('div');
                announcer.setAttribute('aria-live', 'polite');
                announcer.setAttribute('aria-atomic', 'true');
                announcer.className = 'sr-only';
                document.body.appendChild(announcer);
                this.announcer = announcer;
            }

            applyFilters() {
                this.filteredResources = this.resources.filter(resource => {
                    if (this.filters.search && !resource.name.includes(this.filters.search)) {
                        return false;
                    }
                    if (this.filters.type && resource.type !== this.filters.type.toLowerCase()) {
                        return false;
                    }
                    if (this.filters.status !== '' && resource.status !== (this.filters.status === 'true')) {
                        return false;
                    }
                    if (this.filters.department && resource.department !== this.filters.department) {
                        return false;
                    }
                    if (this.filters.location && resource.location !== this.filters.location) {
                        return false;
                    }
                    return true;
                });

                this.applySorting();
                this.updateResourceDisplay();
                this.updateResourceCount();
                this.updateActiveFilters();
                this.announceFilterChange();
            }

            updateResourceDisplay() {
                // Add loading state
                const container = document.getElementById('resourceContainer');
                if (container) {
                    container.classList.add('resource-loading');
                }

                // Animate out current resources
                this.resources.forEach(resource => {
                    resource.element.style.opacity = '0';
                    resource.element.style.transform = 'scale(0.95)';
                });

                setTimeout(() => {
                    // Hide all resources
                    this.resources.forEach(resource => {
                        resource.element.style.display = 'none';
                    });

                    // Show filtered resources with stagger animation
                    this.filteredResources.forEach((resource, index) => {
                        setTimeout(() => {
                            resource.element.style.display = 'block';
                            resource.element.style.opacity = '1';
                            resource.element.style.transform = 'scale(1)';
                        }, index * 50);
                    });

                    // Remove loading state
                    if (container) {
                        container.classList.remove('resource-loading');
                    }

                    this.updateView();
                }, CONFIG.animationDuration);
            }

            announceFilterChange() {
                if (this.announcer) {
                    const count = this.filteredResources.length;
                    const message = `Showing ${count} resource${count !== 1 ? 's' : ''}`;
                    this.announcer.textContent = message;
                }
            }

            handleThemeChange() {
                // Update any theme-dependent functionality
                const isDark = document.documentElement.classList.contains('dark');
                console.log(`Theme changed to: ${isDark ? 'dark' : 'light'}`);
            }

            savePreferences() {
                const preferences = {
                    view: this.currentView,
                    sort: this.currentSort,
                    filters: this.filters
                };
                localStorage.setItem(CONFIG.storageKey, JSON.stringify(preferences));
            }

            loadPreferences() {
                try {
                    const stored = localStorage.getItem(CONFIG.storageKey);
                    return stored ? JSON.parse(stored) : {};
                } catch (e) {
                    console.warn('Failed to load preferences:', e);
                    return {};
                }
            }

            restorePreferences() {
                if (this.preferences.view) {
                    this.currentView = this.preferences.view;
                    document.getElementById(`${this.currentView}View`)?.classList.add('active');
                }

                if (this.preferences.sort) {
                    this.currentSort = this.preferences.sort;
                    const sortSelect = document.getElementById('sortBy');
                    if (sortSelect) {
                        sortSelect.value = this.currentSort;
                    }
                }

                if (this.preferences.filters) {
                    Object.assign(this.filters, this.preferences.filters);
                    // Restore filter UI state
                    Object.keys(this.filters).forEach(key => {
                        const element = document.getElementById(`${key}Filter`) || document.getElementById('searchInput');
                        if (element && this.filters[key]) {
                            element.value = this.filters[key];
                        }
                    });
                }
            }

            // Additional methods from the original ResourceManager class
            loadResources() {
                const resourceCards = document.querySelectorAll('.resource-card');
                this.resources = Array.from(resourceCards).map(card => ({
                    element: card,
                    name: card.querySelector('h3')?.textContent?.toLowerCase() || '',
                    type: card.dataset.type || '',
                    status: card.dataset.status === 'true',
                    department: card.dataset.department || '',
                    location: card.dataset.location || '',
                    rate: parseFloat(card.dataset.rate) || 0
                }));
                this.filteredResources = [...this.resources];
            }

            populateFilterDropdowns() {
                const departments = [...new Set(this.resources.map(r => r.department).filter(d => d))];
                const departmentFilter = document.getElementById('departmentFilter');
                if (departmentFilter && departments.length > 0) {
                    departments.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept;
                        option.textContent = dept;
                        departmentFilter.appendChild(option);
                    });
                }

                const locations = [...new Set(this.resources.map(r => r.location).filter(l => l))];
                const locationFilter = document.getElementById('locationFilter');
                if (locationFilter && locations.length > 0) {
                    locations.forEach(loc => {
                        const option = document.createElement('option');
                        option.value = loc;
                        option.textContent = loc;
                        locationFilter.appendChild(option);
                    });
                }
            }

            applyQuickFilter(filterType) {
                this.clearAllFilters();

                switch (filterType) {
                    case 'active':
                        this.filters.status = 'true';
                        document.getElementById('statusFilter').value = 'true';
                        break;
                    case 'human':
                        this.filters.type = 'human';
                        document.getElementById('typeFilter').value = 'Human';
                        break;
                    case 'equipment':
                        this.filters.type = 'equipment';
                        document.getElementById('typeFilter').value = 'Equipment';
                        break;
                    case 'available':
                        this.filters.status = 'true';
                        document.getElementById('statusFilter').value = 'true';
                        break;
                }

                this.applyFilters();
            }

            applySorting() {
                this.filteredResources.sort((a, b) => {
                    switch (this.currentSort) {
                        case 'name':
                            return a.name.localeCompare(b.name);
                        case 'type':
                            return a.type.localeCompare(b.type);
                        case 'department':
                            return a.department.localeCompare(b.department);
                        case 'rate':
                            return b.rate - a.rate;
                        default:
                            return 0;
                    }
                });
            }

            updateView() {
                const container = document.getElementById('resourceContainer');
                if (!container) return;

                // Get current density setting
                const isCompact = this.preferences.density === 'compact';
                const gapSize = isCompact ? 'gap-4' : 'gap-6';

                switch (this.currentView) {
                    case 'grid':
                        container.className = `grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 ${gapSize} transition-all duration-300`;
                        this.updateResourceCards('grid');
                        break;
                    case 'list':
                        container.className = `space-y-4 transition-all duration-300`;
                        this.updateResourceCards('list');
                        break;
                    case 'table':
                        container.className = `space-y-2 transition-all duration-300`;
                        this.updateResourceCards('table');
                        break;
                }

                // Update view-specific styling
                this.applyViewSpecificStyles();
            }

            updateResourceCards(viewType) {
                const cards = document.querySelectorAll('.resource-card');

                cards.forEach(card => {
                    // Remove existing view classes
                    card.classList.remove('grid-card', 'list-card', 'table-card');

                    // Add view-specific classes
                    switch (viewType) {
                        case 'grid':
                            card.classList.add('grid-card');
                            card.style.display = 'block';
                            break;
                        case 'list':
                            card.classList.add('list-card');
                            card.style.display = 'block';
                            break;
                        case 'table':
                            card.classList.add('table-card');
                            card.style.display = 'block';
                            break;
                    }
                });
            }

            applyViewSpecificStyles() {
                const container = document.getElementById('resourceContainer');
                if (!container) return;

                // Remove existing view classes
                container.classList.remove('view-grid', 'view-list', 'view-table');

                // Add current view class for CSS targeting
                container.classList.add(`view-${this.currentView}`);

                // Announce view change for accessibility
                this.announceViewChange();
            }

            announceViewChange() {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = `View changed to ${this.currentView} layout`;

                document.body.appendChild(announcement);

                // Remove announcement after screen readers have processed it
                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 1000);
            }

            updateResourceCount() {
                const countElement = document.getElementById('resourceCount');
                if (countElement) {
                    countElement.textContent = this.filteredResources.length;
                }
            }

            clearAllFilters() {
                this.filters = {
                    search: '',
                    type: '',
                    status: '',
                    department: '',
                    location: '',
                    skill: '',
                    availability: '',
                    experience: '',
                    costCenter: '',
                    lastActive: '',
                    certification: '',
                    projectCount: '',
                    minRate: '',
                    maxRate: ''
                };

                // Clear all form elements
                const elementsToReset = [
                    'searchInput', 'typeFilter', 'statusFilter', 'departmentFilter', 'locationFilter',
                    'skillFilter', 'availabilityFilter', 'experienceFilter', 'costCenterFilter',
                    'lastActiveFilter', 'certificationFilter', 'projectCountFilter',
                    'minRateFilter', 'maxRateFilter'
                ];

                elementsToReset.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '';
                    }
                });

                // Remove active states from quick filters
                document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                this.applyFilters();
                this.savePreferences();
            }

            setupMultiLineLayouts() {
                // More Actions Dropdown
                const moreActionsBtn = document.getElementById('moreActionsBtn');
                const moreActionsMenu = document.getElementById('moreActionsMenu');

                if (moreActionsBtn && moreActionsMenu) {
                    moreActionsBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        moreActionsMenu.classList.toggle('hidden');
                    });

                    // Close menu when clicking outside
                    document.addEventListener('click', (e) => {
                        if (!moreActionsBtn.contains(e.target) && !moreActionsMenu.contains(e.target)) {
                            moreActionsMenu.classList.add('hidden');
                        }
                    });

                    // Close menu when pressing Escape
                    document.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape') {
                            moreActionsMenu.classList.add('hidden');
                        }
                    });
                }

                // Advanced Filters Toggle
                const toggleAdvancedFilters = document.getElementById('toggleAdvancedFilters');
                const advancedFilters = document.getElementById('advancedFilters');
                const filtersToggleIcon = document.getElementById('filtersToggleIcon');

                if (toggleAdvancedFilters && advancedFilters) {
                    toggleAdvancedFilters.addEventListener('click', (e) => {
                        e.preventDefault();
                        const isHidden = advancedFilters.classList.contains('hidden');

                        if (isHidden) {
                            advancedFilters.classList.remove('hidden');
                            filtersToggleIcon.style.transform = 'rotate(180deg)';
                            toggleAdvancedFilters.setAttribute('aria-expanded', 'true');

                            // Smooth slide down animation with proper height for multi-column layout
                            advancedFilters.style.maxHeight = '0px';
                            advancedFilters.style.opacity = '0';
                            advancedFilters.style.transition = 'all 0.3s ease';

                            requestAnimationFrame(() => {
                                // Calculate proper height for multi-column content
                                const screenWidth = window.innerWidth;
                                let maxHeight = '300px'; // Default for large screens

                                if (screenWidth < 640) {
                                    maxHeight = '400px'; // More height needed for mobile stacking
                                } else if (screenWidth < 1024) {
                                    maxHeight = '350px'; // Medium height for tablet
                                }

                                advancedFilters.style.maxHeight = maxHeight;
                                advancedFilters.style.opacity = '1';
                            });
                        } else {
                            // Smooth slide up animation
                            advancedFilters.style.maxHeight = '0px';
                            advancedFilters.style.opacity = '0';

                            setTimeout(() => {
                                advancedFilters.classList.add('hidden');
                                filtersToggleIcon.style.transform = 'rotate(0deg)';
                                toggleAdvancedFilters.setAttribute('aria-expanded', 'false');
                            }, 300);
                        }
                    });
                }

                // Density Controls
                const compactView = document.getElementById('compactView');
                const comfortableView = document.getElementById('comfortableView');

                if (compactView && comfortableView) {
                    compactView.addEventListener('click', () => {
                        this.setViewDensity('compact');
                    });

                    comfortableView.addEventListener('click', () => {
                        this.setViewDensity('comfortable');
                    });
                }

                // Items per page control
                const itemsPerPage = document.getElementById('itemsPerPage');
                if (itemsPerPage) {
                    itemsPerPage.addEventListener('change', (e) => {
                        this.setItemsPerPage(parseInt(e.target.value));
                    });
                }

                // Save/Load filter sets
                const saveFilters = document.getElementById('saveFilters');
                if (saveFilters) {
                    saveFilters.addEventListener('click', () => {
                        this.saveFilterSet();
                    });
                }

                // Ensure proper wrapping behavior on resize
                window.addEventListener('resize', () => {
                    this.adjustLayoutForScreenSize();
                });

                // Initial layout adjustment
                this.adjustLayoutForScreenSize();
            }

            setViewDensity(density) {
                const container = document.getElementById('resourceContainer');
                const compactBtn = document.getElementById('compactView');
                const comfortableBtn = document.getElementById('comfortableView');

                if (!container || !compactBtn || !comfortableBtn) return;

                // Add transition effect
                container.style.transition = 'all 0.3s ease';

                // Update button states
                this.updateDensityButtonStates(density, compactBtn, comfortableBtn);

                // Update container spacing with smooth transition
                setTimeout(() => {
                    if (density === 'compact') {
                        container.classList.remove('gap-6');
                        container.classList.add('gap-4');
                    } else {
                        container.classList.remove('gap-4');
                        container.classList.add('gap-6');
                    }

                    // Update view to apply new spacing
                    this.updateView();

                    // Remove transition after animation
                    setTimeout(() => {
                        container.style.transition = '';
                    }, 300);
                }, 50);

                this.preferences.density = density;
                this.savePreferences();

                // Announce density change for accessibility
                this.announceDensityChange(density);
            }

            updateDensityButtonStates(density, compactBtn, comfortableBtn) {
                // Reset both buttons
                [compactBtn, comfortableBtn].forEach(btn => {
                    btn.classList.remove('bg-white', 'dark:bg-neutral-700', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
                    btn.classList.add('text-neutral-600', 'dark:text-neutral-400');
                });

                // Set active button
                const activeBtn = density === 'compact' ? compactBtn : comfortableBtn;
                activeBtn.classList.add('bg-white', 'dark:bg-neutral-700', 'text-primary-600', 'dark:text-primary-400', 'shadow-sm');
                activeBtn.classList.remove('text-neutral-600', 'dark:text-neutral-400');
            }

            announceDensityChange(density) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = `Display density changed to ${density}`;

                document.body.appendChild(announcement);

                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 1000);
            }

            updateActiveFilters() {
                const activeFiltersContainer = document.getElementById('activeFilters');
                const activeFiltersList = document.getElementById('activeFiltersList');

                if (!activeFiltersContainer || !activeFiltersList) return;

                // Clear existing filter tags
                activeFiltersList.innerHTML = '';

                const activeFilters = [];

                // Check each filter
                if (this.filters.search) {
                    activeFilters.push({ type: 'search', value: this.filters.search, label: `Search: "${this.filters.search}"` });
                }
                if (this.filters.type) {
                    activeFilters.push({ type: 'type', value: this.filters.type, label: `Type: ${this.filters.type}` });
                }
                if (this.filters.status !== '') {
                    const statusLabel = this.filters.status === 'true' ? 'Active' : 'Inactive';
                    activeFilters.push({ type: 'status', value: this.filters.status, label: `Status: ${statusLabel}` });
                }
                if (this.filters.department) {
                    activeFilters.push({ type: 'department', value: this.filters.department, label: `Dept: ${this.filters.department}` });
                }
                if (this.filters.location) {
                    activeFilters.push({ type: 'location', value: this.filters.location, label: `Location: ${this.filters.location}` });
                }

                if (activeFilters.length > 0) {
                    activeFiltersContainer.classList.remove('hidden');

                    activeFilters.forEach(filter => {
                        const tag = document.createElement('span');
                        tag.className = 'active-filter-tag';
                        tag.innerHTML = `
                            ${filter.label}
                            <button type="button" class="ml-1.5 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200" onclick="window.resourceIndexManager.removeFilter('${filter.type}')" title="Remove filter">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        `;
                        activeFiltersList.appendChild(tag);
                    });
                } else {
                    activeFiltersContainer.classList.add('hidden');
                }
            }

            removeFilter(filterType) {
                switch (filterType) {
                    case 'search':
                        this.filters.search = '';
                        document.getElementById('searchInput').value = '';
                        break;
                    case 'type':
                        this.filters.type = '';
                        document.getElementById('typeFilter').value = '';
                        break;
                    case 'status':
                        this.filters.status = '';
                        const statusFilter = document.getElementById('statusFilter');
                        if (statusFilter) statusFilter.value = '';
                        break;
                    case 'department':
                        this.filters.department = '';
                        document.getElementById('departmentFilter').value = '';
                        break;
                    case 'location':
                        this.filters.location = '';
                        document.getElementById('locationFilter').value = '';
                        break;
                }

                this.applyFilters();
            }

            setItemsPerPage(count) {
                this.preferences.itemsPerPage = count;
                this.savePreferences();
                // Implement pagination logic here
                console.log(`Items per page set to: ${count}`);
            }

            saveFilterSet() {
                const filterName = prompt('Enter a name for this filter set:');
                if (filterName) {
                    const savedFilters = JSON.parse(localStorage.getItem('saved-filter-sets') || '{}');
                    savedFilters[filterName] = {
                        filters: this.filters,
                        sort: this.currentSort,
                        view: this.currentView,
                        savedAt: new Date().toISOString()
                    };
                    localStorage.setItem('saved-filter-sets', JSON.stringify(savedFilters));
                    this.showNotification(`Filter set "${filterName}" saved successfully`, 'success');
                }
            }

            adjustLayoutForScreenSize() {
                const screenWidth = window.innerWidth;

                // Adjust stats badges layout
                const statsBadges = document.querySelector('.stats-badges-container');
                if (statsBadges) {
                    if (screenWidth < 768) {
                        statsBadges.style.maxWidth = '100%';
                    } else if (screenWidth < 1024) {
                        statsBadges.style.maxWidth = '24rem';
                    } else {
                        statsBadges.style.maxWidth = '28rem';
                    }
                }

                // Adjust action buttons layout
                const actionButtons = document.querySelector('.action-buttons-container');
                if (actionButtons) {
                    if (screenWidth < 640) {
                        actionButtons.style.width = '100%';
                        actionButtons.style.justifyContent = 'flex-start';
                    } else {
                        actionButtons.style.width = 'auto';
                        actionButtons.style.justifyContent = 'flex-end';
                    }
                }
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

                const colors = {
                    success: 'bg-emerald-500 text-white',
                    error: 'bg-red-500 text-white',
                    info: 'bg-blue-500 text-white',
                    warning: 'bg-amber-500 text-white'
                };

                notification.className += ` ${colors[type] || colors.info}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Animate in
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0)';
                });

                // Auto remove
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            try {
                if (document.getElementById('resourceContainer')) {
                    console.log('Initializing ResourceIndexManager...');
                    window.resourceIndexManager = new ResourceIndexManager();
                    console.log('ResourceIndexManager initialized successfully');
                } else {
                    console.warn('Resource container not found, skipping ResourceIndexManager initialization');
                }
            } catch (error) {
                console.error('Error initializing ResourceIndexManager:', error);
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.resourceIndexManager) {
                window.resourceIndexManager.savePreferences();
            }
        });

    })();
</script>
