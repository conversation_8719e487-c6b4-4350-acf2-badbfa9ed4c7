<style>
    /* Resource Details Page Styles - Following PM.Tool UI Design System v2.1 */
    
    /* 1. Page-specific component styles */
    .resource-details-container {
        @@apply space-y-8;
    }
    
    /* 2. Enhanced header styling */
    .resource-header {
        position: relative;
        overflow: hidden;
    }
    
    .resource-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(37, 99, 235, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .resource-header:hover::before {
        opacity: 1;
    }
    
    .dark .resource-header::before {
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    }
    
    /* 3. Resource icon enhancements */
    .resource-icon-container {
        position: relative;
        transition: all 0.3s ease;
    }
    
    .resource-icon-container::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-500), var(--primary-600));
        border-radius: inherit;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .resource-icon-container:hover::after {
        opacity: 0.2;
    }
    
    .resource-icon-container:hover {
        transform: scale(1.05);
    }
    
    /* 4. Metrics bar styling */
    .metrics-bar {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(249, 250, 251, 0.9) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(229, 231, 235, 0.8);
    }
    
    .dark .metrics-bar {
        background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.9) 100%);
        border: 1px solid rgba(55, 65, 81, 0.8);
    }
    
    .metrics-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }
    
    .metrics-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s ease;
    }
    
    .metrics-badge:hover::before {
        left: 100%;
    }
    
    .dark .metrics-badge::before {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    }
    
    /* 5. Content section enhancements */
    .content-section {
        transition: all 0.3s ease;
        position: relative;
    }
    
    .content-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .dark .content-section:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    /* 6. Tab navigation styling */
    .tab-navigation {
        border-bottom: 2px solid transparent;
        background: linear-gradient(to right, var(--neutral-200), var(--neutral-100), var(--neutral-200));
    }
    
    .dark .tab-navigation {
        background: linear-gradient(to right, var(--neutral-700), var(--neutral-800), var(--neutral-700));
    }
    
    .tab-button {
        position: relative;
        transition: all 0.3s ease;
        overflow: hidden;
    }
    
    .tab-button::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .tab-button.active::before,
    .tab-button:hover::before {
        transform: scaleX(1);
    }
    
    .tab-button.active {
        background: rgba(59, 130, 246, 0.1);
        color: var(--primary-600);
    }
    
    .dark .tab-button.active {
        background: rgba(96, 165, 250, 0.1);
        color: var(--primary-400);
    }
    
    /* 7. Action button enhancements */
    .action-button-group {
        display: flex;
        gap: 0.75rem;
        align-items: center;
    }
    
    .action-button {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }
    
    .action-button::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }
    
    .action-button:hover::before {
        width: 100%;
        height: 100%;
    }
    
    /* 8. Status indicator animations */
    .status-indicator {
        position: relative;
        animation: pulse-glow 2s ease-in-out infinite;
    }
    
    @@keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
        }
        50% {
            box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
        }
    }
    
    .status-indicator.inactive {
        animation: none;
        opacity: 0.7;
    }
    
    /* 9. Data visualization enhancements */
    .data-chart {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
    }
    
    .data-chart:hover {
        transform: scale(1.02);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .dark .data-chart:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
    
    /* 10. Loading states */
    .details-loading {
        position: relative;
    }
    
    .details-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: shimmer 1.5s infinite;
    }
    
    .dark .details-loading::after {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    }
    
    @@keyframes shimmer {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }
    
    /* 11. Responsive adjustments */
    @@media (max-width: 768px) {
        .resource-header {
            padding: 1.5rem 1rem;
        }
        
        .metrics-bar {
            flex-direction: column;
            gap: 1rem;
        }
        
        .metrics-badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .action-button-group {
            flex-direction: column;
            width: 100%;
        }
        
        .action-button {
            width: 100%;
            justify-content: center;
        }
    }
    
    /* 12. Print styles */
    @@media print {
        .action-button-group,
        .tab-navigation {
            display: none !important;
        }
        
        .content-section {
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
            break-inside: avoid;
        }
        
        .metrics-bar {
            background: white !important;
            border: 1px solid #e5e7eb !important;
        }
    }
    
    /* 13. High contrast mode */
    @@media (prefers-contrast: high) {
        .resource-header,
        .metrics-bar,
        .content-section {
            border-width: 2px;
        }
        
        .metrics-badge {
            border: 1px solid currentColor;
        }
        
        .tab-button {
            border: 1px solid currentColor;
        }
    }
    
    /* 14. Reduced motion support */
    @@media (prefers-reduced-motion: reduce) {
        .resource-icon-container,
        .content-section,
        .action-button,
        .metrics-badge,
        .tab-button {
            transition: none;
        }
        
        .status-indicator {
            animation: none;
        }
        
        .details-loading::after {
            animation: none;
        }
    }
</style>
