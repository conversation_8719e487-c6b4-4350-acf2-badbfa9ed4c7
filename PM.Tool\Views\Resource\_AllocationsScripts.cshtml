<script>
    // Resource Allocations Page Scripts - Following PM.Tool UI Design System v2.1
    
    (function() {
        'use strict';
        
        // Configuration and constants
        const CONFIG = {
            resourceId: @ViewBag.Resource?.Id ?? 0,
            apiEndpoint: '@Url.Action("Allocations", "Resource")',
            createEndpoint: '@Url.Action("CreateAllocation", "Resource")',
            updateEndpoint: '@Url.Action("UpdateAllocation", "Resource")',
            deleteEndpoint: '@Url.Action("DeleteAllocation", "Resource")',
            refreshInterval: 60000, // 1 minute
            animationDuration: 300,
            storageKey: 'resource-allocations-preferences'
        };
        
        // Enhanced Resource Allocations Manager
        class ResourceAllocationsManager {
            constructor() {
                this.currentView = 'table'; // table, calendar, timeline
                this.currentFilter = 'all'; // all, active, pending, completed
                this.allocations = [];
                this.filteredAllocations = [];
                this.refreshTimer = null;
                this.preferences = this.loadPreferences();
                this.isLoading = false;
                
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.setupViewSwitching();
                this.setupFiltering();
                this.setupSorting();
                this.setupThemeHandling();
                this.setupAccessibility();
                this.setupAutoRefresh();
                this.setupKeyboardShortcuts();
                this.setupDragAndDrop();
                this.restorePreferences();
                this.loadAllocations();
            }
            
            bindEvents() {
                // Allocation row interactions
                const allocationRows = document.querySelectorAll('.allocation-row');
                allocationRows.forEach(row => {
                    row.addEventListener('click', (e) => {
                        if (!e.target.closest('.allocation-actions')) {
                            this.selectAllocation(row);
                        }
                    });
                    
                    row.addEventListener('dblclick', () => {
                        this.editAllocation(row.dataset.allocationId);
                    });
                });
                
                // Action button handlers
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.edit-allocation')) {
                        e.preventDefault();
                        const allocationId = e.target.closest('.allocation-row').dataset.allocationId;
                        this.editAllocation(allocationId);
                    }
                    
                    if (e.target.closest('.delete-allocation')) {
                        e.preventDefault();
                        const allocationId = e.target.closest('.allocation-row').dataset.allocationId;
                        this.deleteAllocation(allocationId);
                    }
                    
                    if (e.target.closest('.toggle-status')) {
                        e.preventDefault();
                        const allocationId = e.target.closest('.allocation-row').dataset.allocationId;
                        this.toggleAllocationStatus(allocationId);
                    }
                });
                
                // Bulk actions
                const selectAllCheckbox = document.getElementById('selectAll');
                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', (e) => {
                        this.toggleSelectAll(e.target.checked);
                    });
                }
                
                const bulkActionButtons = document.querySelectorAll('.bulk-action');
                bulkActionButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        const action = button.dataset.action;
                        this.performBulkAction(action);
                    });
                });
                
                // New allocation button
                const newAllocationBtn = document.getElementById('newAllocation');
                if (newAllocationBtn) {
                    newAllocationBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.createNewAllocation();
                    });
                }
                
                // Refresh button
                const refreshBtn = document.getElementById('refreshAllocations');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.refreshAllocations();
                    });
                }
            }
            
            setupViewSwitching() {
                const viewButtons = document.querySelectorAll('.view-toggle');
                viewButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        const newView = button.dataset.view;
                        if (newView !== this.currentView) {
                            this.switchView(newView);
                        }
                    });
                });
            }
            
            setupFiltering() {
                const filterButtons = document.querySelectorAll('.filter-btn');
                filterButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        // Update active state
                        filterButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        
                        const filter = button.dataset.filter;
                        this.applyFilter(filter);
                    });
                });
                
                // Date range filter
                const dateRangeInputs = document.querySelectorAll('.date-range-input');
                dateRangeInputs.forEach(input => {
                    input.addEventListener('change', () => {
                        this.applyDateRangeFilter();
                    });
                });
                
                // Search filter
                const searchInput = document.getElementById('allocationSearch');
                if (searchInput) {
                    let searchTimeout;
                    searchInput.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.applySearchFilter(e.target.value);
                        }, 300);
                    });
                }
            }
            
            setupSorting() {
                const sortableHeaders = document.querySelectorAll('.sortable-header');
                sortableHeaders.forEach(header => {
                    header.addEventListener('click', () => {
                        const column = header.dataset.column;
                        this.sortAllocations(column);
                    });
                });
            }
            
            setupThemeHandling() {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            this.handleThemeChange();
                        }
                    });
                });
                
                observer.observe(document.documentElement, {
                    attributes: true,
                    attributeFilter: ['class']
                });
            }
            
            setupAccessibility() {
                // Add ARIA labels and keyboard navigation
                const allocationRows = document.querySelectorAll('.allocation-row');
                allocationRows.forEach((row, index) => {
                    row.setAttribute('role', 'row');
                    row.setAttribute('aria-label', `Allocation ${index + 1}`);
                    row.setAttribute('tabindex', '0');
                    
                    row.addEventListener('keydown', (e) => {
                        switch (e.key) {
                            case 'Enter':
                            case ' ':
                                e.preventDefault();
                                this.selectAllocation(row);
                                break;
                            case 'Delete':
                                e.preventDefault();
                                this.deleteAllocation(row.dataset.allocationId);
                                break;
                            case 'e':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.editAllocation(row.dataset.allocationId);
                                }
                                break;
                        }
                    });
                });
                
                // Live region for announcements
                const liveRegion = document.createElement('div');
                liveRegion.setAttribute('aria-live', 'polite');
                liveRegion.setAttribute('aria-atomic', 'true');
                liveRegion.className = 'sr-only';
                document.body.appendChild(liveRegion);
                this.liveRegion = liveRegion;
            }
            
            setupAutoRefresh() {
                this.refreshTimer = setInterval(() => {
                    if (!document.hidden && !this.isLoading) {
                        this.refreshAllocations(true); // Silent refresh
                    }
                }, CONFIG.refreshInterval);
                
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        clearInterval(this.refreshTimer);
                    } else {
                        this.setupAutoRefresh();
                    }
                });
            }
            
            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch (e.key) {
                            case 'n':
                                e.preventDefault();
                                this.createNewAllocation();
                                break;
                            case 'r':
                                e.preventDefault();
                                this.refreshAllocations();
                                break;
                            case 'a':
                                e.preventDefault();
                                this.toggleSelectAll(true);
                                break;
                            case 'd':
                                e.preventDefault();
                                this.toggleSelectAll(false);
                                break;
                        }
                    }
                    
                    // View switching shortcuts
                    if (e.altKey) {
                        switch (e.key) {
                            case '1':
                                e.preventDefault();
                                this.switchView('table');
                                break;
                            case '2':
                                e.preventDefault();
                                this.switchView('calendar');
                                break;
                            case '3':
                                e.preventDefault();
                                this.switchView('timeline');
                                break;
                        }
                    }
                });
            }
            
            setupDragAndDrop() {
                // Enable drag and drop for allocation reordering
                const allocationRows = document.querySelectorAll('.allocation-row');
                allocationRows.forEach(row => {
                    row.draggable = true;
                    
                    row.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', row.dataset.allocationId);
                        row.classList.add('dragging');
                    });
                    
                    row.addEventListener('dragend', () => {
                        row.classList.remove('dragging');
                    });
                    
                    row.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        row.classList.add('drag-over');
                    });
                    
                    row.addEventListener('dragleave', () => {
                        row.classList.remove('drag-over');
                    });
                    
                    row.addEventListener('drop', (e) => {
                        e.preventDefault();
                        row.classList.remove('drag-over');
                        
                        const draggedId = e.dataTransfer.getData('text/plain');
                        const targetId = row.dataset.allocationId;
                        
                        if (draggedId !== targetId) {
                            this.reorderAllocations(draggedId, targetId);
                        }
                    });
                });
            }
            
            switchView(newView) {
                this.currentView = newView;
                
                // Update view toggle buttons
                const viewButtons = document.querySelectorAll('.view-toggle');
                viewButtons.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.view === newView);
                });
                
                // Switch view content
                const viewContainers = document.querySelectorAll('.view-container');
                viewContainers.forEach(container => {
                    container.classList.toggle('hidden', !container.classList.contains(`${newView}-view`));
                });
                
                // Initialize view-specific functionality
                switch (newView) {
                    case 'calendar':
                        this.initializeCalendarView();
                        break;
                    case 'timeline':
                        this.initializeTimelineView();
                        break;
                    case 'table':
                        this.initializeTableView();
                        break;
                }
                
                this.savePreferences();
                this.announceViewChange(newView);
            }
            
            applyFilter(filter) {
                this.currentFilter = filter;
                
                this.filteredAllocations = this.allocations.filter(allocation => {
                    switch (filter) {
                        case 'active':
                            return allocation.status === 'Active';
                        case 'pending':
                            return allocation.status === 'Pending';
                        case 'completed':
                            return allocation.status === 'Completed';
                        case 'overdue':
                            return new Date(allocation.endDate) < new Date() && allocation.status !== 'Completed';
                        default:
                            return true;
                    }
                });
                
                this.updateAllocationDisplay();
                this.savePreferences();
                this.announceFilterChange(filter);
            }
            
            selectAllocation(row) {
                // Toggle selection
                const isSelected = row.classList.contains('selected');
                
                if (isSelected) {
                    row.classList.remove('selected');
                    row.setAttribute('aria-selected', 'false');
                } else {
                    row.classList.add('selected');
                    row.setAttribute('aria-selected', 'true');
                }
                
                this.updateBulkActionButtons();
            }
            
            toggleSelectAll(selectAll) {
                const allocationRows = document.querySelectorAll('.allocation-row');
                allocationRows.forEach(row => {
                    if (selectAll) {
                        row.classList.add('selected');
                        row.setAttribute('aria-selected', 'true');
                    } else {
                        row.classList.remove('selected');
                        row.setAttribute('aria-selected', 'false');
                    }
                });
                
                this.updateBulkActionButtons();
            }
            
            updateBulkActionButtons() {
                const selectedRows = document.querySelectorAll('.allocation-row.selected');
                const bulkActionContainer = document.querySelector('.bulk-actions');
                
                if (bulkActionContainer) {
                    bulkActionContainer.style.display = selectedRows.length > 0 ? 'flex' : 'none';
                }
                
                // Update select all checkbox
                const selectAllCheckbox = document.getElementById('selectAll');
                if (selectAllCheckbox) {
                    const allRows = document.querySelectorAll('.allocation-row');
                    selectAllCheckbox.checked = selectedRows.length === allRows.length;
                    selectAllCheckbox.indeterminate = selectedRows.length > 0 && selectedRows.length < allRows.length;
                }
            }
            
            performBulkAction(action) {
                const selectedRows = document.querySelectorAll('.allocation-row.selected');
                const selectedIds = Array.from(selectedRows).map(row => row.dataset.allocationId);
                
                if (selectedIds.length === 0) {
                    this.showNotification('No allocations selected', 'warning');
                    return;
                }
                
                switch (action) {
                    case 'delete':
                        this.bulkDeleteAllocations(selectedIds);
                        break;
                    case 'activate':
                        this.bulkUpdateStatus(selectedIds, 'Active');
                        break;
                    case 'complete':
                        this.bulkUpdateStatus(selectedIds, 'Completed');
                        break;
                    case 'export':
                        this.exportAllocations(selectedIds);
                        break;
                }
            }
            
            refreshAllocations(silent = false) {
                if (this.isLoading) return;
                
                this.isLoading = true;
                
                if (!silent) {
                    this.showLoadingState();
                }
                
                // Simulate API call (replace with actual implementation)
                setTimeout(() => {
                    this.isLoading = false;
                    
                    if (!silent) {
                        this.hideLoadingState();
                        this.showNotification('Allocations refreshed', 'success');
                    }
                    
                    this.updateLastRefreshTime();
                }, 1000);
            }
            
            showLoadingState() {
                const container = document.querySelector('.allocations-container');
                if (container) {
                    container.classList.add('allocations-loading');
                }
            }
            
            hideLoadingState() {
                const container = document.querySelector('.allocations-loading');
                if (container) {
                    container.classList.remove('allocations-loading');
                }
            }
            
            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;
                
                const colors = {
                    success: 'bg-emerald-500 text-white',
                    error: 'bg-red-500 text-white',
                    info: 'bg-blue-500 text-white',
                    warning: 'bg-amber-500 text-white'
                };
                
                notification.className += ` ${colors[type] || colors.info}`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0)';
                });
                
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }
            
            announceViewChange(view) {
                if (this.liveRegion) {
                    this.liveRegion.textContent = `Switched to ${view} view`;
                }
            }
            
            announceFilterChange(filter) {
                if (this.liveRegion) {
                    const count = this.filteredAllocations.length;
                    this.liveRegion.textContent = `Showing ${count} ${filter} allocation${count !== 1 ? 's' : ''}`;
                }
            }
            
            handleThemeChange() {
                const isDark = document.documentElement.classList.contains('dark');
                console.log(`Allocations theme changed to: ${isDark ? 'dark' : 'light'}`);
            }
            
            savePreferences() {
                const preferences = {
                    view: this.currentView,
                    filter: this.currentFilter
                };
                localStorage.setItem(CONFIG.storageKey, JSON.stringify(preferences));
            }
            
            loadPreferences() {
                try {
                    const stored = localStorage.getItem(CONFIG.storageKey);
                    return stored ? JSON.parse(stored) : {};
                } catch (e) {
                    console.warn('Failed to load preferences:', e);
                    return {};
                }
            }
            
            restorePreferences() {
                if (this.preferences.view) {
                    this.switchView(this.preferences.view);
                }
                
                if (this.preferences.filter) {
                    const filterButton = document.querySelector(`[data-filter="${this.preferences.filter}"]`);
                    if (filterButton) {
                        filterButton.click();
                    }
                }
            }
            
            loadAllocations() {
                // Load allocations from DOM or API
                const allocationRows = document.querySelectorAll('.allocation-row');
                this.allocations = Array.from(allocationRows).map(row => ({
                    id: row.dataset.allocationId,
                    status: row.dataset.status,
                    startDate: row.dataset.startDate,
                    endDate: row.dataset.endDate,
                    element: row
                }));
                this.filteredAllocations = [...this.allocations];
            }
            
            updateAllocationDisplay() {
                // Update the display based on current filter
                this.allocations.forEach(allocation => {
                    const shouldShow = this.filteredAllocations.includes(allocation);
                    allocation.element.style.display = shouldShow ? '' : 'none';
                });
            }
            
            updateLastRefreshTime() {
                const timeElement = document.getElementById('lastRefreshTime');
                if (timeElement) {
                    timeElement.textContent = new Date().toLocaleTimeString();
                }
            }
            
            // Placeholder methods for actual implementation
            editAllocation(id) { console.log('Edit allocation:', id); }
            deleteAllocation(id) { console.log('Delete allocation:', id); }
            toggleAllocationStatus(id) { console.log('Toggle status:', id); }
            createNewAllocation() { console.log('Create new allocation'); }
            reorderAllocations(draggedId, targetId) { console.log('Reorder:', draggedId, targetId); }
            bulkDeleteAllocations(ids) { console.log('Bulk delete:', ids); }
            bulkUpdateStatus(ids, status) { console.log('Bulk update status:', ids, status); }
            exportAllocations(ids) { console.log('Export:', ids); }
            initializeCalendarView() { console.log('Initialize calendar view'); }
            initializeTimelineView() { console.log('Initialize timeline view'); }
            initializeTableView() { console.log('Initialize table view'); }
            sortAllocations(column) { console.log('Sort by:', column); }
            applyDateRangeFilter() { console.log('Apply date range filter'); }
            applySearchFilter(query) { console.log('Search:', query); }
            
            destroy() {
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                }
                this.savePreferences();
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.resourceAllocationsManager = new ResourceAllocationsManager();
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.resourceAllocationsManager) {
                window.resourceAllocationsManager.destroy();
            }
        });
        
    })();
</script>
