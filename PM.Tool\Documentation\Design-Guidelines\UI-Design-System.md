# PM.Tool UI Design System & Theme Guidelines

## Overview
This document establishes the comprehensive design system and user experience guidelines for the PM.Tool application, featuring complete light and dark theme support. Based on the improvements made to the MyTasks module, these guidelines ensure consistency, professionalism, and optimal user experience across all views and components in both light and dark modes.

### Key Features
- **Dual Theme Support**: Complete light and dark theme implementations
- **Accessibility First**: WCAG 2.1 AA compliant color contrasts and interactions
- **Performance Optimized**: Efficient theme switching and CSS optimization
- **Component Library**: Comprehensive set of theme-aware components
- **Developer Experience**: Clear implementation patterns and best practices
- **Future-Proof**: Extensible system for new components and features

## 1. Visual Hierarchy & Layout Principles

### 1.1 Page Structure
```
┌─────────────────────────────────────────┐
│ Page Header (with stats & actions)     │ ← Primary level
├─────────────────────────────────────────┤
│ Filter Bar (compact, functional)       │ ← Secondary level
├─────────────────────────────────────────┤
│ View Controls (minimal space)          │ ← Tertiary level
├─────────────────────────────────────────┤
│ Main Content Area (maximum space)      │ ← Content focus
└─────────────────────────────────────────┘
```

**Key Principles:**
- **Content-first approach**: Main content should consume 70-80% of screen space
- **Progressive disclosure**: Show essential info first, details on demand
- **Horizontal efficiency**: Use inline layouts to minimize vertical space consumption
- **Clear separation**: Use borders, spacing, and background changes to define sections

### 1.2 Information Hierarchy
1. **Primary**: Page title, main actions, critical alerts
2. **Secondary**: Filters, navigation, status indicators
3. **Tertiary**: Metadata, pagination, secondary actions
4. **Supporting**: Help text, timestamps, technical details

## 2. Spacing & Layout System

### 2.1 Spacing Scale
```css
/* Base spacing units (Tailwind-based) */
--space-1: 4px;   /* Micro spacing */
--space-2: 8px;   /* Small spacing */
--space-3: 12px;  /* Medium spacing */
--space-4: 16px;  /* Large spacing */
--space-6: 24px;  /* XL spacing */
--space-8: 32px;  /* XXL spacing */
--space-12: 48px; /* Section spacing */
```

### 2.2 Component Spacing Rules

**Header Sections:**
- Outer margin: `mb-8` (32px)
- Inner padding: `px-10 py-8` (40px horizontal, 32px vertical) - Enhanced
- Element gaps: `space-x-10` (40px between major elements) - Enhanced
- Button spacing: `space-x-5` (20px between action buttons) - Enhanced
- Button container: `flex-shrink-0` to prevent button compression

**Filter Bars:**
- Outer margin: `mb-10` (40px) - Enhanced
- Inner padding: `px-10 py-8` (40px horizontal, 32px vertical) - Enhanced
- Section gaps: `gap-8` (32px between filter groups) - Enhanced
- Quick filter gaps: `gap-3` (12px between quick filter buttons) - Enhanced
- Main filter gaps: `gap-6` (24px between main filters) - Enhanced
- Button spacing: `gap-5` (20px between action buttons) - Enhanced

**View Controls:**
- Container: Separate section with background and border
- Inner padding: `px-10 py-6` (40px horizontal, 24px vertical) - Enhanced
- Section margins: `mb-8` (32px) - Enhanced
- Control gaps: `space-x-6` (24px between controls) - Enhanced
- Result count spacing: `space-x-8` (32px) - Enhanced
- Background: `bg-white dark:bg-neutral-800` for definition

**Content Areas:**
- Content padding: `px-10` (40px horizontal) for main content - Enhanced
- Content left margin: `ml-2` (8px) for text/content elements - Prevents edge sticking
- Section spacing: `space-y-8` (32px between sections) - Enhanced
- Grid gaps: `gap-8` (32px between cards) - Enhanced
- List gaps: `space-y-6` (24px between list items) - Enhanced
- List item padding: `px-8 py-6` (32px horizontal, 24px vertical) - Enhanced
- List item content gaps: `space-x-6` (24px between content elements) - Enhanced

**Text and Label Margins:**
- Section titles: `ml-2` (8px) - Prevents sticking to container edge
- Form labels: `ml-2` (8px) - Consistent left margin for readability
- Content grids/lists: `ml-2` (8px) - Subtle margin from container edge

**Form Controls:**
- Input padding: `px-4 py-2.5` (16px horizontal, 10px vertical)
- Button padding: `px-5 py-2.5` (20px horizontal, 10px vertical)
- Control gaps: `gap-3` (12px between related controls)

### 2.3 Icon Spacing Standards
```css
/* Icon-to-text spacing by context */
.badge-icon { margin-right: 8px; }      /* mr-2 */
.button-icon { margin-right: 10px; }    /* mr-2.5 */
.header-icon { margin-right: 12px; }    /* mr-3 */
.list-icon { margin-right: 12px; }      /* mr-3 */
```

## 3. Component Design Patterns

### 3.1 Theme-Aware Header Pattern

#### Light Theme Header
```html
<!-- Light theme page header -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 bg-white shadow-sm">
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-200">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-neutral-900 tracking-tight">[Page Title]</h1>
            <!-- Inline stats badges -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Light theme stats badges -->
                <span class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    <i class="fas fa-tasks mr-2 text-blue-600"></i>24 Active
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Light theme action buttons -->
            <button class="btn-primary btn-medium">
                <i class="fas fa-plus mr-2"></i>New Task
            </button>
        </div>
    </div>
</div>
```

#### Dark Theme Header
```html
<!-- Dark theme page header -->
<div class="mb-8 page-header rounded-xl border border-neutral-700 bg-neutral-800 shadow-lg">
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-700">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-white tracking-tight">[Page Title]</h1>
            <!-- Inline stats badges -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Dark theme stats badges -->
                <span class="inline-flex items-center px-3 py-1.5 bg-blue-900/50 text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-tasks mr-2 text-blue-400"></i>24 Active
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Dark theme action buttons -->
            <button class="btn-primary btn-medium">
                <i class="fas fa-plus mr-2"></i>New Task
            </button>
        </div>
    </div>
</div>
```

#### Universal Header Pattern (Recommended)
```html
<!-- Theme-aware header using Tailwind dark mode utilities -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 shadow-sm dark:shadow-lg">
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">[Page Title]</h1>
            <!-- Inline stats badges -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Theme-aware stats badges -->
                <span class="inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-tasks mr-2 text-blue-600 dark:text-blue-400"></i>24 Active
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Theme-aware action buttons -->
            <button class="btn-primary btn-medium">
                <i class="fas fa-plus mr-2"></i>New Task
            </button>
        </div>
    </div>
</div>
```

**Header Guidelines:**
- Always include page title with consistent typography
- Use inline stats badges for key metrics (max 5 badges)
- Place primary actions on the right
- Hide complex stats on mobile, show essential ones only
- Use semantic colors for different metric types
- Ensure proper contrast in both light and dark themes
- Use theme-aware shadows (subtle in light, more pronounced in dark)

### 3.2 Theme-Aware Stats Badge Pattern

#### Light Theme Badge Examples
```html
<!-- Blue badges for general metrics -->
<span class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
    <i class="fas fa-tasks mr-2 text-blue-600"></i>24 Total
</span>

<!-- Emerald badges for success metrics -->
<span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 text-emerald-800 rounded-full text-xs font-medium">
    <i class="fas fa-check-circle mr-2 text-emerald-600"></i>18 Complete
</span>

<!-- Amber badges for warnings -->
<span class="inline-flex items-center px-3 py-1.5 bg-amber-100 text-amber-800 rounded-full text-xs font-medium">
    <i class="fas fa-clock mr-2 text-amber-600"></i>3 Due Soon
</span>

<!-- Red badges for critical items -->
<span class="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-800 rounded-full text-xs font-medium">
    <i class="fas fa-exclamation-triangle mr-2 text-red-600"></i>2 Overdue
</span>
```

#### Dark Theme Badge Examples
```html
<!-- Blue badges for general metrics -->
<span class="inline-flex items-center px-3 py-1.5 bg-blue-900/50 text-blue-200 rounded-full text-xs font-medium">
    <i class="fas fa-tasks mr-2 text-blue-400"></i>24 Total
</span>

<!-- Emerald badges for success metrics -->
<span class="inline-flex items-center px-3 py-1.5 bg-emerald-900/50 text-emerald-200 rounded-full text-xs font-medium">
    <i class="fas fa-check-circle mr-2 text-emerald-400"></i>18 Complete
</span>

<!-- Amber badges for warnings -->
<span class="inline-flex items-center px-3 py-1.5 bg-amber-900/50 text-amber-200 rounded-full text-xs font-medium">
    <i class="fas fa-clock mr-2 text-amber-400"></i>3 Due Soon
</span>

<!-- Red badges for critical items -->
<span class="inline-flex items-center px-3 py-1.5 bg-red-900/50 text-red-200 rounded-full text-xs font-medium">
    <i class="fas fa-exclamation-triangle mr-2 text-red-400"></i>2 Overdue
</span>
```

#### Universal Badge Pattern (Recommended)
```html
<!-- Theme-aware badges using Tailwind dark mode utilities -->
<span class="inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
    <i class="fas fa-tasks mr-2 text-blue-600 dark:text-blue-400"></i>24 Total
</span>

<span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-xs font-medium">
    <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>18 Complete
</span>

<span class="inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 rounded-full text-xs font-medium">
    <i class="fas fa-clock mr-2 text-amber-600 dark:text-amber-400"></i>3 Due Soon
</span>

<span class="inline-flex items-center px-3 py-1.5 bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 rounded-full text-xs font-medium">
    <i class="fas fa-exclamation-triangle mr-2 text-red-600 dark:text-red-400"></i>2 Overdue
</span>
```

**Badge Color Coding:**
- **Blue**: General metrics, totals, active items
- **Emerald**: Success, completion, positive metrics
- **Amber**: Time-sensitive, due dates, warnings
- **Red**: Critical, overdue, errors
- **Purple**: Analytics, reports, insights

**Theme-Specific Badge Guidelines:**
- **Light Theme**: Use solid color backgrounds (e.g., `bg-blue-100`)
- **Dark Theme**: Use semi-transparent backgrounds (e.g., `bg-blue-900/50`)
- **Icon Colors**: Darker in light theme, lighter in dark theme
- **Text Colors**: High contrast for readability in both themes
- **Consistency**: Maintain semantic meaning across themes

### 3.3 Filter Bar Pattern
```html
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-8">
    <form class="px-6 py-5">
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick filters section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    <!-- Quick filter buttons -->
                </div>
            </div>

            <!-- Separator -->
            <div class="h-8 w-px bg-neutral-300 dark:bg-neutral-600"></div>

            <!-- Main filters -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search and filter controls -->
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-3 ml-auto">
                <!-- Apply/Clear buttons -->
            </div>
        </div>
    </form>
</div>
```

### 3.4 Quick Filter Button Pattern
```html
<button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="[value]">
    <i class="fas fa-[icon] mr-2 text-[color]-500"></i>[Label]
</button>
```

**Quick Filter Guidelines:**
- Use semantic icons and colors
- Keep labels short (1-2 words max)
- Provide visual feedback for active states
- Group related filters together
- Maximum 5-6 quick filters per section

### 3.5 View Controls Pattern
```html
<div class="flex items-center justify-between py-4 mb-6 border-b border-neutral-200 dark:border-neutral-700">
    <div class="flex items-center space-x-5">
        <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">View:</span>
        <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
            <!-- View toggle buttons -->
        </div>
    </div>
    <div class="flex items-center space-x-6">
        <!-- Results info and bulk actions -->
    </div>
</div>
```

## 4. Typography System

### 4.1 Font Scale
```css
/* Typography hierarchy */
.text-3xl { font-size: 30px; line-height: 36px; } /* Page titles */
.text-2xl { font-size: 24px; line-height: 32px; } /* Section titles */
.text-xl  { font-size: 20px; line-height: 28px; } /* Subsection titles */
.text-lg  { font-size: 18px; line-height: 28px; } /* Large body text */
.text-base{ font-size: 16px; line-height: 24px; } /* Body text */
.text-sm  { font-size: 14px; line-height: 20px; } /* Small text */
.text-xs  { font-size: 12px; line-height: 16px; } /* Micro text */
```

### 4.2 Font Weight Usage
- **font-bold (700)**: Page titles, important numbers
- **font-semibold (600)**: Section headers, button text, labels
- **font-medium (500)**: Body text, form labels
- **font-normal (400)**: Secondary text, descriptions

### 4.3 Theme-Aware Typography Colors

#### Light Theme Typography
```css
/* Light Theme Text Colors */
.text-primary-light { color: #111827; }     /* Main headings, body text */
.text-secondary-light { color: #374151; }   /* Supporting text, labels */
.text-tertiary-light { color: #6b7280; }    /* Captions, metadata */
.text-quaternary-light { color: #9ca3af; }  /* Disabled, placeholder */
.text-inverse-light { color: #ffffff; }     /* Text on dark backgrounds */

/* Light Theme Semantic Text */
.text-success-light { color: #047857; }     /* Success messages */
.text-warning-light { color: #b45309; }     /* Warning messages */
.text-error-light { color: #b91c1c; }       /* Error messages */
.text-info-light { color: #1d4ed8; }        /* Info messages */
```

#### Dark Theme Typography
```css
/* Dark Theme Text Colors */
.text-primary-dark { color: #f9fafb; }      /* Main headings, body text */
.text-secondary-dark { color: #e5e7eb; }    /* Supporting text, labels */
.text-tertiary-dark { color: #9ca3af; }     /* Captions, metadata */
.text-quaternary-dark { color: #6b7280; }   /* Disabled, placeholder */
.text-inverse-dark { color: #111827; }      /* Text on light backgrounds */

/* Dark Theme Semantic Text */
.text-success-dark { color: #a7f3d0; }      /* Success messages */
.text-warning-dark { color: #fde68a; }      /* Warning messages */
.text-error-dark { color: #fecaca; }        /* Error messages */
.text-info-dark { color: #bfdbfe; }         /* Info messages */
```

#### Tailwind CSS Implementation
```css
/* Using Tailwind's dark mode utilities for text */
.text-primary { @apply text-neutral-900 dark:text-neutral-50; }
.text-secondary { @apply text-neutral-700 dark:text-neutral-200; }
.text-tertiary { @apply text-neutral-500 dark:text-neutral-400; }
.text-quaternary { @apply text-neutral-400 dark:text-neutral-500; }

/* Semantic text colors with theme awareness */
.text-success { @apply text-emerald-700 dark:text-emerald-300; }
.text-warning { @apply text-amber-700 dark:text-amber-300; }
.text-error { @apply text-red-700 dark:text-red-300; }
.text-info { @apply text-blue-700 dark:text-blue-300; }
```

#### Typography Contrast Guidelines

**Light Theme Contrast Ratios:**
- Primary text on white: 13.6:1 (AAA)
- Secondary text on white: 7.0:1 (AAA)
- Tertiary text on white: 4.5:1 (AA)
- Quaternary text on white: 3.0:1 (AA Large)

**Dark Theme Contrast Ratios:**
- Primary text on dark: 15.8:1 (AAA)
- Secondary text on dark: 8.2:1 (AAA)
- Tertiary text on dark: 4.5:1 (AA)
- Quaternary text on dark: 3.0:1 (AA Large)

## 5. Color System & Theme Guidelines

### 5.1 Complete Color Palette

#### Primary Brand Colors
```css
/* Primary Blue Scale */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-200: #bfdbfe;
--primary-300: #93c5fd;
--primary-400: #60a5fa;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;
--primary-800: #1e40af;
--primary-900: #1e3a8a;
--primary-950: #172554;
```

#### Semantic Color Scales
```css
/* Success - Emerald Scale */
--success-50: #ecfdf5;
--success-100: #d1fae5;
--success-200: #a7f3d0;
--success-300: #6ee7b7;
--success-400: #34d399;
--success-500: #10b981;
--success-600: #059669;
--success-700: #047857;
--success-800: #065f46;
--success-900: #064e3b;

/* Warning - Amber Scale */
--warning-50: #fffbeb;
--warning-100: #fef3c7;
--warning-200: #fde68a;
--warning-300: #fcd34d;
--warning-400: #fbbf24;
--warning-500: #f59e0b;
--warning-600: #d97706;
--warning-700: #b45309;
--warning-800: #92400e;
--warning-900: #78350f;

/* Error - Red Scale */
--error-50: #fef2f2;
--error-100: #fee2e2;
--error-200: #fecaca;
--error-300: #fca5a5;
--error-400: #f87171;
--error-500: #ef4444;
--error-600: #dc2626;
--error-700: #b91c1c;
--error-800: #991b1b;
--error-900: #7f1d1d;

/* Info - Blue Scale */
--info-50: #eff6ff;
--info-100: #dbeafe;
--info-200: #bfdbfe;
--info-300: #93c5fd;
--info-400: #60a5fa;
--info-500: #3b82f6;
--info-600: #2563eb;
--info-700: #1d4ed8;
--info-800: #1e40af;
--info-900: #1e3a8a;

/* Neutral Gray Scale */
--neutral-50: #f9fafb;
--neutral-100: #f3f4f6;
--neutral-200: #e5e7eb;
--neutral-300: #d1d5db;
--neutral-400: #9ca3af;
--neutral-500: #6b7280;
--neutral-600: #4b5563;
--neutral-700: #374151;
--neutral-800: #1f2937;
--neutral-900: #111827;
--neutral-950: #030712;
```

### 5.2 Light Theme Color System

#### Light Theme Backgrounds
```css
/* Light Theme Background Hierarchy */
--bg-primary-light: #ffffff;           /* Main content areas */
--bg-secondary-light: #f9fafb;         /* Secondary sections */
--bg-tertiary-light: #f3f4f6;          /* Subtle backgrounds */
--bg-accent-light: #eff6ff;            /* Accent backgrounds */
--bg-overlay-light: rgba(255, 255, 255, 0.95); /* Modal overlays */

/* Light Theme Surface Colors */
--surface-elevated-light: #ffffff;      /* Cards, panels */
--surface-sunken-light: #f3f4f6;       /* Input fields, wells */
--surface-interactive-light: #f9fafb;   /* Hover states */
```

#### Light Theme Text Colors
```css
/* Light Theme Text Hierarchy */
--text-primary-light: #111827;         /* Main headings, body text */
--text-secondary-light: #374151;       /* Supporting text */
--text-tertiary-light: #6b7280;        /* Captions, metadata */
--text-quaternary-light: #9ca3af;      /* Disabled, placeholder */
--text-inverse-light: #ffffff;         /* Text on dark backgrounds */

/* Light Theme Link Colors */
--link-primary-light: #2563eb;         /* Default links */
--link-hover-light: #1d4ed8;           /* Link hover state */
--link-visited-light: #7c3aed;         /* Visited links */
```

#### Light Theme Border Colors
```css
/* Light Theme Borders */
--border-primary-light: #e5e7eb;       /* Default borders */
--border-secondary-light: #d1d5db;     /* Subtle borders */
--border-accent-light: #2563eb;        /* Focus, active borders */
--border-error-light: #ef4444;         /* Error state borders */
--border-success-light: #10b981;       /* Success state borders */
```

### 5.3 Dark Theme Color System

#### Dark Theme Backgrounds
```css
/* Dark Theme Background Hierarchy */
--bg-primary-dark: #111827;            /* Main content areas */
--bg-secondary-dark: #1f2937;          /* Secondary sections */
--bg-tertiary-dark: #374151;           /* Subtle backgrounds */
--bg-accent-dark: #1e3a8a;             /* Accent backgrounds */
--bg-overlay-dark: rgba(17, 24, 39, 0.95); /* Modal overlays */

/* Dark Theme Surface Colors */
--surface-elevated-dark: #1f2937;      /* Cards, panels */
--surface-sunken-dark: #111827;        /* Input fields, wells */
--surface-interactive-dark: #374151;   /* Hover states */
```

#### Dark Theme Text Colors
```css
/* Dark Theme Text Hierarchy */
--text-primary-dark: #f9fafb;          /* Main headings, body text */
--text-secondary-dark: #e5e7eb;        /* Supporting text */
--text-tertiary-dark: #9ca3af;         /* Captions, metadata */
--text-quaternary-dark: #6b7280;       /* Disabled, placeholder */
--text-inverse-dark: #111827;          /* Text on light backgrounds */

/* Dark Theme Link Colors */
--link-primary-dark: #60a5fa;          /* Default links */
--link-hover-dark: #93c5fd;            /* Link hover state */
--link-visited-dark: #a78bfa;          /* Visited links */
```

#### Dark Theme Border Colors
```css
/* Dark Theme Borders */
--border-primary-dark: #374151;        /* Default borders */
--border-secondary-dark: #4b5563;      /* Subtle borders */
--border-accent-dark: #60a5fa;         /* Focus, active borders */
--border-error-dark: #f87171;          /* Error state borders */
--border-success-dark: #34d399;        /* Success state borders */
```

### 5.4 Theme-Aware Semantic Colors

#### Status Colors for Both Themes
```css
/* Light Theme Status Colors */
--status-success-light: #10b981;
--status-success-bg-light: #ecfdf5;
--status-success-text-light: #047857;

--status-warning-light: #f59e0b;
--status-warning-bg-light: #fffbeb;
--status-warning-text-light: #b45309;

--status-error-light: #ef4444;
--status-error-bg-light: #fef2f2;
--status-error-text-light: #b91c1c;

--status-info-light: #3b82f6;
--status-info-bg-light: #eff6ff;
--status-info-text-light: #1d4ed8;

/* Dark Theme Status Colors */
--status-success-dark: #34d399;
--status-success-bg-dark: #064e3b;
--status-success-text-dark: #a7f3d0;

--status-warning-dark: #fbbf24;
--status-warning-bg-dark: #78350f;
--status-warning-text-dark: #fde68a;

--status-error-dark: #f87171;
--status-error-bg-dark: #7f1d1d;
--status-error-text-dark: #fecaca;

--status-info-dark: #60a5fa;
--status-info-bg-dark: #1e3a8a;
--status-info-text-dark: #bfdbfe;
```

### 5.5 Theme Implementation Guidelines

#### CSS Custom Properties Setup
```css
:root {
  /* Default to light theme */
  --bg-primary: var(--bg-primary-light);
  --bg-secondary: var(--bg-secondary-light);
  --bg-tertiary: var(--bg-tertiary-light);
  --text-primary: var(--text-primary-light);
  --text-secondary: var(--text-secondary-light);
  --text-tertiary: var(--text-tertiary-light);
  --border-primary: var(--border-primary-light);
  --border-secondary: var(--border-secondary-light);
}

[data-theme="dark"] {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --bg-tertiary: var(--bg-tertiary-dark);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-tertiary: var(--text-tertiary-dark);
  --border-primary: var(--border-primary-dark);
  --border-secondary: var(--border-secondary-dark);
}

/* Alternative: CSS class-based approach */
.dark {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --bg-tertiary: var(--bg-tertiary-dark);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-tertiary: var(--text-tertiary-dark);
  --border-primary: var(--border-primary-dark);
  --border-secondary: var(--border-secondary-dark);
}
```

#### Tailwind CSS Dark Mode Configuration
```css
/* Using Tailwind's dark mode utilities */
.bg-primary { @apply bg-white dark:bg-neutral-900; }
.bg-secondary { @apply bg-neutral-50 dark:bg-neutral-800; }
.bg-tertiary { @apply bg-neutral-100 dark:bg-neutral-700; }

.text-primary { @apply text-neutral-900 dark:text-neutral-50; }
.text-secondary { @apply text-neutral-700 dark:text-neutral-200; }
.text-tertiary { @apply text-neutral-500 dark:text-neutral-400; }

.border-primary { @apply border-neutral-200 dark:border-neutral-700; }
.border-secondary { @apply border-neutral-300 dark:border-neutral-600; }
```

### 5.6 Color Usage Guidelines

**Status Colors:**
- **Blue**: Active, in-progress, informational
- **Emerald**: Completed, success, positive metrics
- **Amber**: Pending, due soon, warnings
- **Red**: Overdue, errors, critical items
- **Purple**: Analytics, insights, special features

**Background Colors:**
- **Primary**: Main content areas (white/neutral-900)
- **Secondary**: Supporting sections (neutral-50/neutral-800)
- **Tertiary**: Subtle backgrounds (neutral-100/neutral-700)
- **Semantic**: Status-specific backgrounds with appropriate opacity

## 6. Interactive Elements

### 6.1 Enhanced Button System

#### Enhanced Button Size Standards with Generous Padding
```css
/* Large buttons - for primary actions, hero sections */
.btn-large {
    @apply inline-flex items-center px-8 py-3 text-base font-semibold rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
    /* 32px horizontal padding for generous spacing */
}

/* Medium buttons - default size for most use cases */
.btn-medium {
    @apply inline-flex items-center px-6 py-2.5 text-sm font-semibold rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
    /* 24px horizontal padding - increased from 20px */
}

/* Small buttons - for compact layouts, secondary actions */
.btn-small {
    @apply inline-flex items-center px-5 py-2 text-sm font-medium rounded-md shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
    /* 20px horizontal padding - increased from 16px */
}

/* Extra small buttons - for tight spaces, table actions */
.btn-xs {
    @apply inline-flex items-center px-4 py-1.5 text-xs font-medium rounded transition-all duration-200;
    /* 16px horizontal padding - increased from 12px */
}
```

#### Button Padding Rationale
- **Generous horizontal padding** ensures buttons don't feel cramped
- **Consistent padding scale** (32px, 24px, 20px, 16px) follows 4px grid
- **Professional appearance** with adequate breathing room
- **Touch-friendly targets** especially on mobile devices
- **Visual balance** between text and button boundaries

#### Professional Button Variants
```css
/* Primary button - Modern gradient with enhanced shadows */
.btn-primary {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 1px solid #2563eb;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #1d4ed8;
    box-shadow: 0 6px 20px 0 rgba(37, 99, 235, 0.4);
    transform: translateY(-2px);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.3);
}

/* Secondary button - Clean with subtle shadows */
.btn-secondary {
    @apply text-neutral-700 dark:text-neutral-200 font-medium bg-white dark:bg-neutral-800;
    border: 1.5px solid #e5e7eb;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.dark .btn-secondary {
    border-color: #374151;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.dark .btn-secondary:hover {
    background: #374151;
    border-color: #4b5563;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.3);
}

/* Tertiary/Ghost button - Minimal with hover effects */
.btn-tertiary {
    @apply text-neutral-600 dark:text-neutral-400 font-medium bg-transparent;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.btn-tertiary:hover {
    @apply text-neutral-800 dark:text-neutral-200;
    background: rgba(0, 0, 0, 0.04);
    border-color: #e5e7eb;
    transform: translateY(-1px);
}

.dark .btn-tertiary:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: #374151;
}

/* Danger button - Strong visual warning */
.btn-danger {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid #dc2626;
    box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.25);
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-color: #b91c1c;
    box-shadow: 0 6px 20px 0 rgba(220, 38, 38, 0.4);
    transform: translateY(-2px);
}

/* Success button - Positive action emphasis */
.btn-success {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid #059669;
    box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.25);
    transition: all 0.2s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
    box-shadow: 0 6px 20px 0 rgba(5, 150, 105, 0.4);
    transform: translateY(-2px);
}
```

#### Icon Spacing Standards
```css
/* Icon spacing by button size */
.btn-large .icon { margin-right: 12px; }    /* mr-3 */
.btn-medium .icon { margin-right: 10px; }   /* mr-2.5 */
.btn-small .icon { margin-right: 8px; }     /* mr-2 */
.btn-xs .icon { margin-right: 6px; }        /* mr-1.5 */

/* Icon-only buttons */
.btn-icon-large {
    @apply w-12 h-12 justify-center;
}

.btn-icon-medium {
    @apply w-10 h-10 justify-center;
}

.btn-icon-small {
    @apply w-8 h-8 justify-center;
}

.btn-icon-xs {
    @apply w-6 h-6 justify-center;
}
```

#### Button States
```css
/* Loading state */
.btn-loading {
    @apply opacity-75 cursor-not-allowed;
}

.btn-loading .icon {
    @apply animate-spin;
}

/* Disabled state */
.btn:disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* Active state */
.btn-active {
    @apply ring-2 ring-primary-500 ring-offset-2;
}
```

### 6.2 Button Usage Guidelines

#### When to Use Each Button Type

**Primary Buttons:**
- Main call-to-action on a page (limit to 1 per section)
- Form submissions (Save, Submit, Create)
- Destructive actions with confirmation (Delete, Remove)
- Navigation to important flows (Get Started, Sign Up)

**Secondary Buttons:**
- Alternative actions (Cancel, Back, Edit)
- Less important actions alongside primary
- Navigation buttons (View Details, Learn More)
- Export/Import functions

**Tertiary Buttons:**
- Subtle actions that don't need emphasis
- Inline actions within content
- Toggle buttons in compact layouts
- Helper actions (Copy, Share, Refresh)

#### Button Sizing Guidelines

**Large Buttons (px-6 py-3):**
- Hero sections and landing pages
- Mobile primary actions
- Important form submissions
- Call-to-action sections

**Medium Buttons (px-5 py-2.5) - Default:**
- Standard page actions
- Form buttons
- Navigation elements
- Most common use case

**Small Buttons (px-4 py-2):**
- Table row actions
- Compact layouts
- Secondary actions in tight spaces
- Filter and sort controls

**Extra Small Buttons (px-3 py-1.5):**
- Dense data tables
- Inline editing actions
- Tag-like interactive elements
- Minimal space requirements

#### Icon Guidelines for Buttons

**Icon Selection:**
- Use icons that clearly represent the action
- Maintain consistency across similar actions
- Prefer solid icons for buttons (fas fa-*)
- Use outline icons sparingly for subtle actions

**Icon Spacing by Button Size:**
```css
/* Large buttons */
.btn-large i { margin-right: 12px; }

/* Medium buttons */
.btn-medium i { margin-right: 10px; }

/* Small buttons */
.btn-small i { margin-right: 8px; }

/* Extra small buttons */
.btn-xs i { margin-right: 6px; }
```

**Icon-Only Buttons:**
- Always include descriptive title attribute
- Use for space-constrained layouts
- Maintain minimum 44px touch targets on mobile
- Consider accessibility for screen readers

#### Button Grouping

**Horizontal Groups:**
```html
<div class="flex items-center space-x-3">
    <button class="btn-primary btn-medium">Primary Action</button>
    <button class="btn-secondary btn-medium">Secondary Action</button>
    <button class="btn-tertiary btn-medium">Tertiary Action</button>
</div>
```

**Segmented Controls:**
```html
<div class="inline-flex rounded-lg shadow-sm" role="group">
    <button class="btn-segment-left">Option 1</button>
    <button class="btn-segment-middle">Option 2</button>
    <button class="btn-segment-right">Option 3</button>
</div>
```

### 6.3 Form Controls
```css
/* Standard input */
.form-input {
    @apply px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200;
}

/* Select dropdown */
.form-select {
    @apply px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200;
}
```

### 6.3 Hover & Focus States
- **Hover**: Subtle lift effect (`transform: translateY(-1px)`)
- **Focus**: 2px ring with primary color
- **Active**: Pressed state (`transform: translateY(0)`)
- **Disabled**: Reduced opacity (0.5) and no interactions

## 7. Animation, Transitions & Theme-Aware Effects

### 7.1 Standard Transitions
```css
/* Default transition for most elements */
.transition-default {
    transition: all 0.2s ease;
}

/* Theme-aware hover effects */
.hover-lift:hover {
    transform: translateY(-1px);
}

/* Light theme hover shadows */
.hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dark theme hover shadows */
.dark .hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Focus animations */
.focus-ring:focus {
    ring: 2px;
    ring-color: var(--primary-500);
    ring-offset: 2px;
}
```

### 7.2 Theme-Aware Shadow System

#### Light Theme Shadows
```css
/* Light theme shadow scale */
.shadow-xs-light { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-sm-light { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md-light { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg-light { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl-light { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
```

#### Dark Theme Shadows
```css
/* Dark theme shadow scale - more pronounced for depth */
.shadow-xs-dark { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2); }
.shadow-sm-dark { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2); }
.shadow-md-dark { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2); }
.shadow-lg-dark { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3); }
.shadow-xl-dark { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4); }
```

#### Universal Shadow Classes
```css
/* Theme-aware shadow utilities */
.shadow-xs { @apply shadow-xs-light dark:shadow-xs-dark; }
.shadow-sm { @apply shadow-sm-light dark:shadow-sm-dark; }
.shadow-md { @apply shadow-md-light dark:shadow-md-dark; }
.shadow-lg { @apply shadow-lg-light dark:shadow-lg-dark; }
.shadow-xl { @apply shadow-xl-light dark:shadow-xl-dark; }

/* Colored shadows for interactive elements */
.shadow-primary {
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
}
.dark .shadow-primary {
    box-shadow: 0 4px 14px 0 rgba(96, 165, 250, 0.3);
}

.shadow-success {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.25);
}
.dark .shadow-success {
    box-shadow: 0 4px 14px 0 rgba(52, 211, 153, 0.3);
}
```

### 7.3 Theme-Aware Special Effects

#### Shimmer Effect for Both Themes
```css
/* Light theme shimmer */
@keyframes shimmer-light {
    0% { left: -100%; }
    100% { left: 100%; }
}

.shimmer-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer-light 2s infinite;
}

/* Dark theme shimmer */
@keyframes shimmer-dark {
    0% { left: -100%; }
    100% { left: 100%; }
}

.shimmer-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer-dark 2s infinite;
}

/* Universal shimmer class */
.shimmer {
    position: relative;
    overflow: hidden;
}

.shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    animation: shimmer-light 2s infinite;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

.dark .shimmer::before {
    animation: shimmer-dark 2s infinite;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
}
```

#### Glow Effects
```css
/* Light theme glow */
.glow-light {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Dark theme glow */
.glow-dark {
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.4);
}

/* Universal glow */
.glow {
    transition: box-shadow 0.3s ease;
}

.glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.dark .glow:hover {
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.4);
}
```

### 7.4 Loading Animations

#### Theme-Aware Skeleton Loading
```css
/* Light theme skeleton */
.skeleton-light {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* Dark theme skeleton */
.skeleton-dark {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Universal skeleton class */
.skeleton {
    border-radius: 4px;
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.dark .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
}
```

#### Spinner Animations
```css
/* Theme-aware spinner */
.spinner {
    border: 2px solid transparent;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.dark .spinner {
    border-top-color: #60a5fa;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 8. Responsive Design Guidelines

### 8.1 Breakpoint Strategy
```css
/* Mobile first approach */
.mobile-first {
    /* Base styles for mobile */
}

@media (min-width: 768px) {
    /* Tablet styles */
}

@media (min-width: 1024px) {
    /* Desktop styles */
}

@media (min-width: 1280px) {
    /* Large desktop styles */
}
```

### 8.2 Responsive Patterns

**Header Adaptation:**
- Mobile: Stack title and actions, hide non-essential stats
- Tablet: Show essential stats, compact actions
- Desktop: Full layout with all elements

**Filter Adaptation:**
- Mobile: Vertical stack, essential filters only
- Tablet: Horizontal with wrapping
- Desktop: Single row, all filters visible

**Content Adaptation:**
- Mobile: Single column, full width
- Tablet: Flexible columns
- Desktop: Multi-column with optimal spacing

## 9. Accessibility Guidelines

### 9.1 Touch Targets
- Minimum 44px touch targets for mobile
- Adequate spacing between interactive elements (8px minimum)
- Clear visual separation for focus indication

### 9.2 Color Contrast
- Text contrast ratio: 4.5:1 minimum
- Interactive element contrast: 3:1 minimum
- Focus indicators: High contrast with background

### 9.3 Keyboard Navigation
- Logical tab order
- Visible focus indicators
- Skip links for main content
- Escape key support for modals/dropdowns

## 10. Performance Guidelines

### 10.1 CSS Optimization
- Use CSS transforms for animations (GPU acceleration)
- Minimize repaints with proper layering
- Optimize transition timing (200ms standard)

### 10.2 Image & Icon Guidelines
- Use SVG icons when possible
- Optimize icon sizes (14px, 16px, 20px, 24px)
- Consistent icon style (FontAwesome or similar)

## 11. Implementation Checklist

### 11.1 New Page/View Checklist
- [ ] Header follows standard pattern with title and stats
- [ ] Filter bar uses compact horizontal layout
- [ ] View controls minimize vertical space
- [ ] Main content area gets maximum space allocation
- [ ] Consistent spacing scale applied throughout
- [ ] Icon spacing follows standards (mr-2, mr-2.5, mr-3)
- [ ] Color usage follows semantic guidelines
- [ ] Typography hierarchy properly implemented
- [ ] Responsive behavior tested on all breakpoints
- [ ] Accessibility requirements met
- [ ] Hover/focus states implemented
- [ ] Loading and error states designed

### 11.2 Component Review Criteria
- **Space Efficiency**: Does it minimize vertical space consumption?
- **Visual Hierarchy**: Is the information hierarchy clear?
- **Consistency**: Does it follow established patterns?
- **Accessibility**: Are all a11y requirements met?
- **Responsiveness**: Does it work on all screen sizes?
- **Performance**: Are animations smooth and efficient?

## 12. Examples & References

### 12.1 Inspiration Sources
- **Azure DevOps**: Professional enterprise interface
- **GitHub**: Clean, developer-focused design
- **Linear**: Modern, efficient task management
- **Notion**: Flexible, content-first approach

### 12.2 Anti-Patterns to Avoid
- ❌ Large stats cards consuming main content space
- ❌ Vertical filter layouts on desktop
- ❌ Inconsistent icon spacing
- ❌ Poor contrast ratios
- ❌ Cramped touch targets on mobile
- ❌ Excessive animation or visual noise
- ❌ Inconsistent spacing scales

## 13. Theme Implementation Guide

### 13.1 Theme Detection & Switching

#### JavaScript Theme Management
```javascript
// Theme detection and management
class ThemeManager {
    constructor() {
        this.theme = this.getStoredTheme() || this.getSystemTheme();
        this.applyTheme(this.theme);
        this.setupSystemThemeListener();
    }

    getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    getStoredTheme() {
        return localStorage.getItem('theme');
    }

    setTheme(theme) {
        this.theme = theme;
        localStorage.setItem('theme', theme);
        this.applyTheme(theme);
    }

    applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.classList.remove('dark');
            document.documentElement.setAttribute('data-theme', 'light');
        }
    }

    setupSystemThemeListener() {
        window.matchMedia('(prefers-color-scheme: dark)')
            .addEventListener('change', (e) => {
                if (!this.getStoredTheme()) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
    }

    toggle() {
        this.setTheme(this.theme === 'dark' ? 'light' : 'dark');
    }
}

// Initialize theme manager
const themeManager = new ThemeManager();
```

#### Theme Toggle Component
```html
<!-- Theme toggle button -->
<button
    id="theme-toggle"
    class="inline-flex items-center justify-center w-10 h-10 rounded-lg border border-neutral-300 dark:border-neutral-600 bg-white dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200"
    title="Toggle theme"
    onclick="themeManager.toggle()"
>
    <!-- Light mode icon -->
    <i class="fas fa-sun text-sm dark:hidden"></i>
    <!-- Dark mode icon -->
    <i class="fas fa-moon text-sm hidden dark:inline-block"></i>
</button>
```

### 13.2 CSS Implementation Strategies

#### Strategy 1: Tailwind CSS Dark Mode (Recommended)
```css
/* Configure Tailwind for dark mode */
module.exports = {
  darkMode: 'class', // or 'media' for system preference
  theme: {
    extend: {
      colors: {
        // Custom color palette
      }
    }
  }
}

/* Usage in HTML */
<div class="bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white">
  Content that adapts to theme
</div>
```

#### Strategy 2: CSS Custom Properties
```css
:root {
  --color-bg-primary: #ffffff;
  --color-text-primary: #111827;
  --color-border-primary: #e5e7eb;
}

[data-theme="dark"] {
  --color-bg-primary: #111827;
  --color-text-primary: #f9fafb;
  --color-border-primary: #374151;
}

.component {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border-color: var(--color-border-primary);
}
```

#### Strategy 3: CSS-in-JS (for React/Vue components)
```javascript
// Styled components with theme
const Button = styled.button`
  background-color: ${props => props.theme.colors.bg.primary};
  color: ${props => props.theme.colors.text.primary};
  border: 1px solid ${props => props.theme.colors.border.primary};

  &:hover {
    background-color: ${props => props.theme.colors.bg.secondary};
  }
`;

// Theme provider
const lightTheme = {
  colors: {
    bg: { primary: '#ffffff', secondary: '#f9fafb' },
    text: { primary: '#111827', secondary: '#374151' },
    border: { primary: '#e5e7eb' }
  }
};

const darkTheme = {
  colors: {
    bg: { primary: '#111827', secondary: '#1f2937' },
    text: { primary: '#f9fafb', secondary: '#e5e7eb' },
    border: { primary: '#374151' }
  }
};
```

### 13.3 Component Theme Adaptation

#### Form Controls Theme Adaptation
```html
<!-- Theme-aware form inputs -->
<input
    type="text"
    class="w-full px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
    placeholder="Enter text..."
>

<!-- Theme-aware select dropdown -->
<select class="w-full px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
    <option>Select option</option>
</select>
```

#### Card Component Theme Adaptation
```html
<!-- Theme-aware card -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm dark:shadow-lg p-6">
    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-3">Card Title</h3>
    <p class="text-neutral-600 dark:text-neutral-300">Card content that adapts to the current theme.</p>
</div>
```

### 13.4 Theme Testing Guidelines

#### Visual Testing Checklist
- [ ] All text maintains proper contrast ratios
- [ ] Interactive elements are clearly visible in both themes
- [ ] Focus states are prominent in both themes
- [ ] Hover states provide appropriate feedback
- [ ] Loading states are visible in both themes
- [ ] Error states are clearly distinguishable
- [ ] Success states are appropriately highlighted

#### Automated Testing
```javascript
// Theme contrast testing
describe('Theme Accessibility', () => {
  test('light theme meets contrast requirements', () => {
    // Test contrast ratios for light theme
  });

  test('dark theme meets contrast requirements', () => {
    // Test contrast ratios for dark theme
  });

  test('theme switching preserves functionality', () => {
    // Test that all features work in both themes
  });
});
```

### 13.5 Performance Considerations

#### CSS Optimization for Themes
```css
/* Avoid theme-specific CSS that causes layout shifts */
.component {
  /* Use consistent sizing across themes */
  min-height: 44px;

  /* Optimize transitions */
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* Preload theme-specific assets */
<link rel="preload" href="/icons/dark-theme-icons.woff2" as="font" type="font/woff2" crossorigin>
```

#### JavaScript Performance
```javascript
// Debounce theme changes to avoid excessive DOM updates
const debouncedThemeChange = debounce((theme) => {
  applyTheme(theme);
}, 100);

// Use CSS custom properties for better performance
function applyTheme(theme) {
  const root = document.documentElement;
  const themeColors = theme === 'dark' ? darkColors : lightColors;

  Object.entries(themeColors).forEach(([property, value]) => {
    root.style.setProperty(`--${property}`, value);
  });
}
```

## 14. Code Organization & Partial Views

### 14.1 View Structure Guidelines

#### Separation of Concerns Pattern
Following the principle of separation of concerns, views should be organized into distinct partial views for better maintainability and reusability:

```
Views/
├── [Controller]/
│   ├── [Action].cshtml           # Main view (markup only)
│   ├── _[Action]Styles.cshtml    # Styles partial
│   └── _[Action]Scripts.cshtml   # Scripts partial
└── Shared/
    ├── Components/
    │   ├── _ComponentName.cshtml
    │   ├── _ComponentNameStyles.cshtml
    │   └── _ComponentNameScripts.cshtml
    └── Partials/
        ├── _PartialName.cshtml
        ├── _PartialNameStyles.cshtml
        └── _PartialNameScripts.cshtml
```

#### Main View Structure
```html
@{
    ViewData["Title"] = "Page Title";
}

<!-- Main content markup only -->
<div class="page-container">
    <!-- Page content here -->
</div>

@section Styles {
    <partial name="_PageNameStyles" />
}

@section Scripts {
    <partial name="_PageNameScripts" />
}
```

### 14.2 Styles Partial Guidelines

#### Styles Partial Structure (`_[ViewName]Styles.cshtml`)
```html
<style>
    /* Page-specific styles following design system */

    /* 1. Component-specific styles */
    .page-specific-component {
        /* Follow design system spacing and colors */
        @apply px-8 py-6 bg-white dark:bg-neutral-800;
    }

    /* 2. Animation and transition overrides */
    .custom-animation {
        transition: all 0.2s ease;
    }

    /* 3. Responsive adjustments */
    @media (max-width: 768px) {
        .mobile-specific {
            /* Mobile-specific styles */
        }
    }

    /* 4. Theme-specific overrides */
    .dark .dark-specific {
        /* Dark theme specific styles */
    }
</style>
```

#### Styles Guidelines
- **Design System First**: Use Tailwind classes primarily, custom CSS only when necessary
- **Theme Awareness**: Always include dark mode variants
- **Component Scoping**: Use specific class names to avoid conflicts
- **Performance**: Minimize custom CSS, prefer utility classes
- **Responsive**: Include mobile-first responsive design

### 14.3 Scripts Partial Guidelines

#### Scripts Partial Structure (`_[ViewName]Scripts.cshtml`)
```html
<script>
    // Page-specific JavaScript following design patterns

    (function() {
        'use strict';

        // 1. Configuration and constants
        const CONFIG = {
            apiEndpoint: '@Url.Action("ApiEndpoint")',
            pageSize: 20,
            debounceDelay: 300
        };

        // 2. Class-based organization
        class PageManager {
            constructor() {
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadInitialData();
                this.setupThemeHandling();
            }

            bindEvents() {
                // Event binding with proper delegation
            }

            setupThemeHandling() {
                // Theme-aware functionality
            }
        }

        // 3. Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            new PageManager();
        });

        // 4. Cleanup and memory management
        window.addEventListener('beforeunload', () => {
            // Cleanup code
        });
    })();
</script>
```

#### Scripts Guidelines
- **IIFE Pattern**: Wrap code in immediately invoked function expressions
- **Class-based Organization**: Use ES6 classes for complex functionality
- **Event Delegation**: Use proper event delegation for dynamic content
- **Memory Management**: Include cleanup code for preventing memory leaks
- **Theme Integration**: Respect current theme settings
- **Performance**: Debounce expensive operations
- **Error Handling**: Include proper error handling and logging

### 14.4 Component Partial Guidelines

#### Component Structure
```html
<!-- _ComponentName.cshtml -->
@model ComponentViewModel

<div class="component-container" data-component="component-name">
    <!-- Component markup -->
</div>

<!-- _ComponentNameStyles.cshtml -->
<style>
    .component-container {
        /* Component-specific styles */
    }
</style>

<!-- _ComponentNameScripts.cshtml -->
<script>
    // Component-specific JavaScript
    window.ComponentName = class {
        constructor(element) {
            this.element = element;
            this.init();
        }

        init() {
            // Component initialization
        }
    };
</script>
```

### 14.5 Implementation Benefits

#### Maintainability
- **Clear Separation**: Styles and scripts are separated from markup
- **Reusability**: Partial views can be reused across different pages
- **Modularity**: Each concern is handled in its own file
- **Team Collaboration**: Different team members can work on different aspects

#### Performance
- **Conditional Loading**: Load styles/scripts only when needed
- **Caching**: Better browser caching of separated resources
- **Minification**: Easier to minify and optimize separated files
- **Bundle Optimization**: Better control over bundling strategies

#### Development Experience
- **IntelliSense**: Better IDE support for separated concerns
- **Debugging**: Easier to debug specific functionality
- **Testing**: Easier to unit test separated JavaScript
- **Code Review**: Cleaner code reviews with separated concerns

### 14.6 Naming Conventions

#### File Naming
- **Main View**: `ViewName.cshtml`
- **Styles Partial**: `_ViewNameStyles.cshtml`
- **Scripts Partial**: `_ViewNameScripts.cshtml`
- **Component**: `_ComponentName.cshtml`
- **Component Styles**: `_ComponentNameStyles.cshtml`
- **Component Scripts**: `_ComponentNameScripts.cshtml`

#### CSS Class Naming
- **Page-specific**: `.page-name-specific-class`
- **Component-specific**: `.component-name-element`
- **State classes**: `.is-active`, `.is-loading`, `.is-disabled`
- **Theme classes**: `.theme-light`, `.theme-dark`

#### JavaScript Naming
- **Classes**: `PascalCase` (e.g., `ResourceManager`)
- **Methods**: `camelCase` (e.g., `bindEvents`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `API_ENDPOINT`)
- **Private methods**: `_camelCase` (e.g., `_handleClick`)

### 14.7 Best Practices

#### Styles Best Practices
- Use Tailwind utilities first, custom CSS only when necessary
- Always include dark mode variants
- Follow mobile-first responsive design
- Use CSS custom properties for theme-aware values
- Minimize specificity conflicts

#### Scripts Best Practices
- Use modern JavaScript (ES6+) features
- Implement proper error handling
- Include accessibility considerations
- Use debouncing for expensive operations
- Implement proper cleanup to prevent memory leaks

#### Integration Best Practices
- Ensure styles and scripts work together seamlessly
- Test theme switching functionality
- Validate responsive behavior
- Check accessibility compliance
- Optimize for performance

## 15. Future Guidelines

This design system will be expanded with additional guidelines for:

- **Data Visualization**: Charts, graphs, and analytics components with theme support
- **Form Design**: Complex forms, validation, and input patterns for both themes
- **Navigation**: Menu systems, breadcrumbs, and wayfinding with theme adaptation
- **Content Management**: Tables, lists, and data presentation in light and dark modes
- **Notification Systems**: Alerts, toasts, and status messages with theme-aware styling
- **Modal & Overlay**: Dialogs, popovers, and contextual interfaces for both themes
- **Animation Systems**: Theme-aware animations and micro-interactions
- **Print Styles**: Theme-appropriate print stylesheets
- **Micro-frontend Architecture**: Guidelines for component-based development
- **Progressive Web App**: PWA-specific design patterns and guidelines

---

**Document Information:**
- **Version**: 2.1 - Theme Support + Code Organization
- **Last Updated**: Enhanced with comprehensive theme guidelines and partial view organization patterns
- **Theme Support**: Full light/dark mode implementation with accessibility compliance
- **Code Organization**: Separation of styles and scripts into partial views for better maintainability
- **Next Review**: After partial view implementation and developer feedback collection

**Note**: This design system should be treated as a living document, updated as new patterns emerge and user feedback is incorporated. All new components should be reviewed against these guidelines to ensure consistency, accessibility, and quality across the PM.Tool application in both light and dark themes.

**Implementation Priority:**
1. **Phase 1**: Implement core theme infrastructure and color system
2. **Phase 2**: Update existing components with theme support
3. **Phase 3**: Add theme toggle functionality and user preferences
4. **Phase 4**: Optimize performance and add advanced theme features
