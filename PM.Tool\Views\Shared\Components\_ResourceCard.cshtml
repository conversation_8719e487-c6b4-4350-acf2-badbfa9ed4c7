@model PM.Tool.Core.Entities.Resource

@{
    string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user-tie",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-question-circle"
        };
    }

    string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-amber-500 to-amber-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-emerald-500 to-emerald-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}

<div class="resource-card transition-all duration-200 hover:shadow-md dark:hover:shadow-lg hover:-translate-y-0.5"
     data-type="@Model.Type.ToString().ToLower()"
     data-status="@Model.IsActive.ToString().ToLower()"
     data-department="@(Model.Department ?? "")"
     data-location="@(Model.Location ?? "")"
     data-rate="@Model.HourlyRate">
    <div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm dark:shadow-lg">
        <!-- Card Header -->
        <div class="px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <div class="w-12 h-12 @GetResourceTypeIconBg(Model.Type) rounded-lg flex items-center justify-center">
                        <i class="@GetResourceTypeIcon(Model.Type) text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@Model.Name</h3>
                        <p class="text-sm text-neutral-600 dark:text-neutral-300 mt-1">@Model.Type</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    @if (Model.IsActive)
                    {
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200">
                            <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>Active
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-200">
                            <i class="fas fa-times-circle mr-2 text-neutral-600 dark:text-neutral-400"></i>Inactive
                        </span>
                    }
                </div>
            </div>
        </div>

        <!-- Card Body -->
        <div class="px-8 py-6 space-y-6">
            @if (!string.IsNullOrEmpty(Model.Description))
            {
                <p class="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">@Model.Description</p>
            }

            <!-- Resource Details -->
            <div class="space-y-4">
                @if (!string.IsNullOrEmpty(Model.Department))
                {
                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                        <i class="fas fa-building w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                        <span>@Model.Department</span>
                    </div>
                }
                @if (!string.IsNullOrEmpty(Model.Location))
                {
                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                        <i class="fas fa-map-marker-alt w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                        <span>@Model.Location</span>
                    </div>
                }
                @if (Model.Type == PM.Tool.Core.Entities.ResourceType.Human)
                {
                    <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                        <i class="fas fa-clock w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                        <span>@Model.Capacity hrs/day</span>
                    </div>
                    @if (Model.HourlyRate > 0)
                    {
                        <div class="flex items-center text-sm text-neutral-600 dark:text-neutral-300">
                            <i class="fas fa-dollar-sign w-4 h-4 mr-3 text-neutral-400 dark:text-neutral-500"></i>
                            <span>@Model.HourlyRate.ToString("C")/hr</span>
                        </div>
                    }
                }
            </div>

            <!-- Skills (for Human resources) -->
            @if (Model.Type == PM.Tool.Core.Entities.ResourceType.Human && !string.IsNullOrEmpty(Model.Skills))
            {
                <div>
                    <h4 class="text-xs font-semibold text-neutral-500 dark:text-neutral-400 mb-3">Skills</h4>
                    <div class="flex flex-wrap gap-2">
                        @{
                            var skills = Model.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries);
                            var displaySkills = skills.Take(3);
                            var remainingCount = skills.Length - 3;
                        }
                        @foreach (var skill in displaySkills)
                        {
                            <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                                @skill.Trim()
                            </span>
                        }
                        @if (remainingCount > 0)
                        {
                            <span class="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300">
                                +@remainingCount more
                            </span>
                        }
                    </div>
                </div>
            }
        </div>

        <!-- Card Footer with Actions -->
        <div class="px-8 py-6 bg-neutral-50 dark:bg-neutral-700/50 border-t border-neutral-200 dark:border-neutral-700 rounded-b-xl">
            <div class="flex items-center justify-between">
                <small class="text-neutral-500 dark:text-neutral-400">
                    Updated @(Model.UpdatedAt?.ToString("MMM dd") ?? "N/A")
                </small>
                <div class="flex items-center space-x-2">
                    <a asp-controller="Resource" asp-action="Details" asp-route-id="@Model.Id"
                       class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 hover:border-blue-300 dark:hover:border-blue-600 rounded-lg transition-all duration-200"
                       title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a asp-controller="Resource" asp-action="Edit" asp-route-id="@Model.Id"
                       class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-amber-50 dark:hover:bg-amber-900/20 hover:text-amber-600 dark:hover:text-amber-400 hover:border-amber-300 dark:hover:border-amber-600 rounded-lg transition-all duration-200"
                       title="Edit Resource">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a asp-controller="Resource" asp-action="Allocations" asp-route-id="@Model.Id"
                       class="inline-flex items-center justify-center w-9 h-9 text-sm bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 text-neutral-600 dark:text-neutral-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 hover:border-purple-300 dark:hover:border-purple-600 rounded-lg transition-all duration-200"
                       title="View Allocations">
                        <i class="fas fa-calendar-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
