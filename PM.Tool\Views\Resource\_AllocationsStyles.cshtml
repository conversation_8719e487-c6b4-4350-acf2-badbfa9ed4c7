<style>
    /* Resource Allocations Page Styles - Following PM.Tool UI Design System v2.1 */
    
    /* 1. Page-specific component styles */
    .allocations-container {
        @@apply space-y-8;
    }
    
    /* 2. Enhanced allocation table styling */
    .allocations-table {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.95) 100%);
        backdrop-filter: blur(10px);
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .dark .allocations-table {
        background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.95) 100%);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    }
    
    .allocations-table:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .dark .allocations-table:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    }
    
    /* 3. Allocation row enhancements */
    .allocation-row {
        transition: all 0.2s ease;
        position: relative;
        border-left: 3px solid transparent;
    }
    
    .allocation-row:hover {
        background: rgba(59, 130, 246, 0.05);
        border-left-color: var(--primary-500);
        transform: translateX(4px);
    }
    
    .dark .allocation-row:hover {
        background: rgba(96, 165, 250, 0.1);
    }
    
    .allocation-row.active {
        background: rgba(16, 185, 129, 0.1);
        border-left-color: var(--success-500);
    }
    
    .allocation-row.pending {
        background: rgba(245, 158, 11, 0.1);
        border-left-color: var(--warning-500);
    }
    
    .allocation-row.completed {
        background: rgba(16, 185, 129, 0.05);
        border-left-color: var(--success-500);
        opacity: 0.8;
    }
    
    /* 4. Status badge animations */
    .status-badge {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .status-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s ease;
    }
    
    .status-badge:hover::before {
        left: 100%;
    }
    
    .status-badge.active {
        animation: pulse-success 2s ease-in-out infinite;
    }
    
    @@keyframes pulse-success {
        0%, 100% {
            box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
        }
        50% {
            box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
        }
    }
    
    /* 5. Calendar view enhancements */
    .calendar-container {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }
    
    .dark .calendar-container {
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        border-color: rgba(51, 65, 85, 0.8);
    }
    
    .calendar-day {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }
    
    .calendar-day:hover {
        transform: scale(1.05);
        z-index: 10;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .calendar-day.has-allocation {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
    }
    
    .calendar-day.has-allocation::after {
        content: '';
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 6px;
        height: 6px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
    }
    
    /* 6. Timeline view styling */
    .timeline-container {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline-container::before {
        content: '';
        position: absolute;
        left: 0.75rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, var(--primary-500), var(--primary-300));
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -2.25rem;
        top: 0.5rem;
        width: 12px;
        height: 12px;
        background: var(--primary-500);
        border: 3px solid white;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .dark .timeline-item::before {
        border-color: var(--neutral-800);
    }
    
    .timeline-item:hover::before {
        transform: scale(1.3);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .timeline-item.active::before {
        background: var(--success-500);
        animation: pulse-timeline 2s ease-in-out infinite;
    }
    
    @@keyframes pulse-timeline {
        0%, 100% {
            box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
        }
        50% {
            box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
        }
    }
    
    /* 7. Allocation form enhancements */
    .allocation-form {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(229, 231, 235, 0.8);
        border-radius: 1rem;
        padding: 2rem;
        transition: all 0.3s ease;
    }
    
    .dark .allocation-form {
        background: rgba(31, 41, 55, 0.8);
        border-color: rgba(55, 65, 81, 0.8);
    }
    
    .allocation-form:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
    }
    
    .dark .allocation-form:hover {
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.3);
    }
    
    /* 8. Progress indicators */
    .progress-bar {
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
        height: 6px;
        border-radius: 3px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: progress-shimmer 2s infinite;
    }
    
    @@keyframes progress-shimmer {
        0% {
            left: -100%;
        }
        100% {
            left: 100%;
        }
    }
    
    /* 9. Action button groups */
    .allocation-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.3s ease;
    }
    
    .allocation-row:hover .allocation-actions {
        opacity: 1;
        transform: translateX(0);
    }
    
    .allocation-action-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }
    
    .allocation-action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    /* 10. Loading states */
    .allocations-loading {
        position: relative;
    }
    
    .allocations-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: loading-sweep 1.5s infinite;
    }
    
    .dark .allocations-loading::after {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    }
    
    @@keyframes loading-sweep {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }
    
    /* 11. Responsive adjustments */
    @@media (max-width: 768px) {
        .allocations-table {
            margin: 0 -1rem;
            border-radius: 0;
        }
        
        .allocation-row {
            padding: 1rem;
        }
        
        .allocation-actions {
            opacity: 1;
            transform: none;
            flex-direction: column;
        }
        
        .timeline-container {
            padding-left: 1rem;
        }
        
        .timeline-container::before {
            left: 0.25rem;
        }
        
        .timeline-item::before {
            left: -1.75rem;
        }
        
        .calendar-container {
            padding: 1rem;
        }
    }
    
    /* 12. Print styles */
    @@media print {
        .allocation-actions,
        .allocation-form {
            display: none !important;
        }
        
        .allocations-table {
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
        }
        
        .allocation-row {
            border-left: 2px solid #e5e7eb !important;
            background: white !important;
        }
        
        .timeline-container::before {
            background: #e5e7eb !important;
        }
    }
    
    /* 13. High contrast mode */
    @@media (prefers-contrast: high) {
        .allocations-table,
        .allocation-form,
        .calendar-container {
            border-width: 2px;
        }
        
        .status-badge {
            border: 1px solid currentColor;
        }
        
        .allocation-action-btn {
            border-width: 2px;
        }
    }
    
    /* 14. Reduced motion support */
    @@media (prefers-reduced-motion: reduce) {
        .allocation-row,
        .status-badge,
        .calendar-day,
        .timeline-item,
        .allocation-form,
        .allocation-actions {
            transition: none;
        }
        
        .status-badge.active,
        .timeline-item.active {
            animation: none;
        }
        
        .progress-bar::after,
        .allocations-loading::after {
            animation: none;
        }
    }
</style>
