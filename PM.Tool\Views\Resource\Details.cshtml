@model PM.Tool.Core.Entities.Resource

@{
    ViewData["Title"] = "Resource Details";
    ViewData["Subtitle"] = $"{Model.Type} Resource Details";
}

<!-- Page Header -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 shadow-sm dark:shadow-lg">
    <div class="flex items-center justify-between px-10 py-8 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-10">
            <div class="w-20 h-20 @GetResourceTypeIconBg(Model.Type) rounded-xl flex items-center justify-center">
                <i class="@GetResourceTypeIcon(Model.Type) text-white text-3xl"></i>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-neutral-900 dark:text-white tracking-tight">@Model.Name</h1>
                <div class="flex items-center space-x-4 mt-3">
                    <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200">
                        <i class="@GetResourceTypeIcon(Model.Type) mr-2 text-blue-600 dark:text-blue-400"></i>
                        @Model.Type
                    </span>
                    @if (Model.IsActive)
                    {
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200">
                            <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>
                            Active
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200">
                            <i class="fas fa-times-circle mr-2 text-red-600 dark:text-red-400"></i>
                            Inactive
                        </span>
                    }
                </div>
            </div>
        </div>

        <div class="flex items-center space-x-5 flex-shrink-0">
            @{
                ViewData["Text"] = "Edit Resource";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "View Allocations";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-calendar-alt";
                ViewData["Href"] = Url.Action("Allocations", new { id = Model.Id });
                ViewData["Size"] = "medium";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
    @if (!string.IsNullOrEmpty(Model.Description))
    {
        <div class="px-10 py-6">
            <p class="text-sm text-neutral-600 dark:text-neutral-300 ml-2">@Model.Description</p>
        </div>
    }
</div>

<!-- Quick Stats Bar -->
<div class="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm dark:shadow-lg mb-8">
    <div class="px-10 py-8">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-neutral-900 dark:text-white">Resource Metrics</h2>
            <div class="flex items-center space-x-6">
                @if (Model.HourlyRate > 0)
                {
                    <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-sm font-medium">
                            <i class="fas fa-dollar-sign mr-2 text-emerald-600 dark:text-emerald-400"></i>@Model.HourlyRate.ToString("C")/hr
                        </span>
                    </div>
                }
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
                        <i class="fas fa-clock mr-2 text-blue-600 dark:text-blue-400"></i>@Model.Capacity hrs/day
                    </span>
                </div>
                @if (Model.HourlyRate > 0)
                {
                    <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1.5 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 rounded-full text-sm font-medium">
                            <i class="fas fa-calculator mr-2 text-purple-600 dark:text-purple-400"></i>@((Model.Capacity * Model.HourlyRate).ToString("C"))/day
                        </span>
                    </div>
                }
                <div class="flex items-center space-x-3">
                    @if (Model.IsActive)
                    {
                        <span class="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-sm font-medium">
                            <i class="fas fa-check-circle mr-2 text-emerald-600 dark:text-emerald-400"></i>Available
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-3 py-1.5 bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 rounded-full text-sm font-medium">
                            <i class="fas fa-times-circle mr-2 text-red-600 dark:text-red-400"></i>Unavailable
                        </span>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column - Details -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Basic Information</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Type</label>
                        <p class="text-lg font-medium text-neutral-900 dark:text-dark-100">@Model.Type</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Department))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Department</label>
                            <p class="text-lg font-medium text-neutral-900 dark:text-dark-100">@Model.Department</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Location))
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Location</label>
                            <p class="text-lg font-medium text-neutral-900 dark:text-dark-100">@Model.Location</p>
                        </div>
                    }

                    <div>
                        <label class="block text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Created</label>
                        <p class="text-lg font-medium text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <div class="mt-6 pt-6 border-t border-neutral-200 dark:border-dark-200">
                        <label class="block text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</label>
                        <p class="text-neutral-900 dark:text-dark-100 leading-relaxed">@Model.Description</p>
                    </div>
                }
            </div>
        </div>

            <!-- Contact Information -->
            @if (!string.IsNullOrEmpty(Model.Email) || !string.IsNullOrEmpty(Model.Phone))
            {
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                            <i class="fas fa-address-book mr-2 text-primary-500"></i>
                            Contact Information
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if (!string.IsNullOrEmpty(Model.Email))
                            {
                                <div>
                                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Email</label>
                                    <a href="mailto:@Model.Email" class="text-primary-600 hover:text-primary-700 dark:text-primary-400">
                                        <i class="fas fa-envelope mr-1"></i>
                                        @Model.Email
                                    </a>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.Phone))
                            {
                                <div>
                                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone</label>
                                    <a href="tel:@Model.Phone" class="text-primary-600 hover:text-primary-700 dark:text-primary-400">
                                        <i class="fas fa-phone mr-1"></i>
                                        @Model.Phone
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Skills -->
            @if (!string.IsNullOrEmpty(Model.Skills))
            {
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                            <i class="fas fa-star mr-2 text-primary-500"></i>
                            Skills & Expertise
                        </h3>
                    </div>
                    <div class="card-body-custom">
                        <div class="flex flex-wrap gap-2">
                            @foreach (var skill in Model.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <span class="badge-info">@skill.Trim()</span>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Recent Allocations -->
            @if (ViewBag.Allocations != null)
            {
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                                <i class="fas fa-calendar-alt mr-2 text-primary-500"></i>
                                Recent Allocations
                            </h3>
                            <a asp-action="Allocations" asp-route-id="@Model.Id" class="text-primary-600 hover:text-primary-700 text-sm">
                                View All <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        @{
                            var allocations = ViewBag.Allocations as IEnumerable<PM.Tool.Core.Entities.ResourceAllocation>;
                            var recentAllocations = allocations?.Take(5) ?? new List<PM.Tool.Core.Entities.ResourceAllocation>();
                        }

                        @if (recentAllocations.Any())
                        {
                            <div class="space-y-3">
                                @foreach (var allocation in recentAllocations)
                                {
                                    <div class="flex justify-between items-center p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                        <div>
                                            <p class="font-medium text-neutral-900 dark:text-dark-100">
                                                @allocation.Project?.Name ?? "Unknown Project"
                                            </p>
                                            <p class="text-sm text-neutral-600 dark:text-dark-300">
                                                @allocation.StartDate.ToString("MMM dd") - @allocation.EndDate.ToString("MMM dd, yyyy")
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-medium text-neutral-900 dark:text-dark-100">
                                                @allocation.AllocationPercentage%
                                            </p>
                                            <p class="text-sm text-neutral-600 dark:text-dark-300">
                                                @allocation.AllocatedHours hrs
                                            </p>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-8">
                                <i class="fas fa-calendar-times text-4xl text-neutral-400 dark:text-dark-500 mb-4"></i>
                                <p class="text-neutral-600 dark:text-dark-300">No allocations found</p>
                                <a asp-action="CreateAllocation" asp-route-resourceId="@Model.Id" class="btn-primary mt-4">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create Allocation
                                </a>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

        <!-- Right Column - Quick Stats -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Actions</h3>
                </div>
                <div class="card-body-custom space-y-3">
                    <a asp-action="CreateAllocation" asp-route-resourceId="@Model.Id" class="btn-outline-primary w-full">
                        <i class="fas fa-plus mr-2"></i>
                        New Allocation
                    </a>
                    <a asp-action="Utilization" class="btn-outline-secondary w-full">
                        <i class="fas fa-chart-line mr-2"></i>
                        View Utilization
                    </a>
                    <a asp-action="Available" class="btn-outline-secondary w-full">
                        <i class="fas fa-search mr-2"></i>
                        Check Availability
                    </a>
                </div>
            </div>

            <!-- Resource Metadata -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Information</h3>
                </div>
                <div class="card-body-custom space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Created</label>
                        <p class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                    </div>

                    @if (Model.UpdatedAt.HasValue)
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Last Updated</label>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-cube"
        };
    }

    private string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
